from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from blog.models import Article
from django.utils import timezone
import random


class Command(BaseCommand):
    help = 'Create sample Arabic blog data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--articles',
            type=int,
            default=20,
            help='Number of articles to create (default: 20)'
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Creating sample Arabic blog data...'))
        
        # Create or get admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'مدير',
                'last_name': 'المدونة',
                'is_staff': True,
                'is_superuser': True,
            }
        )
        
        if created:
            admin_user.set_password('admin123')
            admin_user.save()
            self.stdout.write(self.style.SUCCESS(f'Created admin user: {admin_user.username}'))
        
        # Sample Arabic article data
        sample_articles = [
            {
                'title': 'مقدمة في البرمجة باللغة العربية',
                'content': '''
                البرمجة هي فن وعلم في آن واحد، وهي من أهم المهارات في العصر الحديث. في هذا المقال، سنتعرف على أساسيات البرمجة وكيفية البدء في تعلمها.

                ## ما هي البرمجة؟

                البرمجة هي عملية كتابة تعليمات للحاسوب لتنفيذ مهام محددة. هذه التعليمات تُكتب بلغات برمجة مختلفة مثل Python و JavaScript و Java.

                ## لماذا تعلم البرمجة؟

                1. **فرص عمل واسعة**: سوق العمل في مجال التقنية في نمو مستمر
                2. **حل المشكلات**: البرمجة تطور مهارات التفكير المنطقي
                3. **الإبداع**: يمكنك بناء تطبيقات وحلول مبتكرة
                4. **المرونة**: يمكن العمل من أي مكان

                ## كيف تبدأ؟

                ننصح المبتدئين بالبدء بلغة Python لسهولتها وقوتها. يمكنك البدء بتعلم المفاهيم الأساسية مثل المتغيرات والحلقات والدوال.

                البرمجة رحلة ممتعة ومفيدة، والمهم هو البدء والاستمرار في التعلم والممارسة.
                ''',
                'tags': 'برمجة, تقنية, تعليم, Python',
                'excerpt': 'تعرف على أساسيات البرمجة وكيفية البدء في تعلمها خطوة بخطوة'
            },
            {
                'title': 'الذكاء الاصطناعي وتأثيره على المستقبل',
                'content': '''
                الذكاء الاصطناعي أصبح جزءاً لا يتجزأ من حياتنا اليومية، من محركات البحث إلى التطبيقات الذكية على هواتفنا.

                ## تعريف الذكاء الاصطناعي

                الذكاء الاصطناعي هو قدرة الآلات على محاكاة الذكاء البشري وتنفيذ مهام تتطلب تفكيراً وتعلماً.

                ## تطبيقات الذكاء الاصطناعي

                - **الطب**: تشخيص الأمراض وتطوير العلاجات
                - **النقل**: السيارات ذاتية القيادة
                - **التعليم**: أنظمة التعلم التكيفية
                - **التجارة**: توصيات المنتجات والخدمات

                ## التحديات والفرص

                رغم الفوائد الهائلة، هناك تحديات مثل الخصوصية وأمان البيانات. لكن الفرص أكبر بكثير من التحديات.

                المستقبل مشرق مع الذكاء الاصطناعي، والمهم هو الاستعداد له بالتعلم والتطوير المستمر.
                ''',
                'tags': 'ذكاء اصطناعي, تقنية, مستقبل, تطوير',
                'excerpt': 'استكشف عالم الذكاء الاصطناعي وتأثيره على مختلف جوانب الحياة'
            },
            {
                'title': 'تطوير المواقع الإلكترونية: دليل شامل',
                'content': '''
                تطوير المواقع الإلكترونية مهارة أساسية في العصر الرقمي. سنتعرف في هذا المقال على أساسيات تطوير المواقع.

                ## أنواع تطوير المواقع

                ### تطوير الواجهة الأمامية (Frontend)
                - HTML: هيكل الموقع
                - CSS: تصميم وتنسيق الموقع
                - JavaScript: التفاعل والحركة

                ### تطوير الخلفية (Backend)
                - خوادم الويب
                - قواعد البيانات
                - لغات البرمجة مثل Python و PHP

                ## الأدوات والتقنيات الحديثة

                - **إطارات العمل**: React, Vue.js, Django
                - **أدوات البناء**: Webpack, Vite
                - **إدارة النسخ**: Git و GitHub

                ## نصائح للمبتدئين

                1. ابدأ بالأساسيات
                2. مارس بناء مشاريع حقيقية
                3. تابع أحدث التطورات
                4. انضم لمجتمعات المطورين

                تطوير المواقع مجال واسع ومثير، والمهم هو البدء والاستمرار في التعلم.
                ''',
                'tags': 'تطوير مواقع, HTML, CSS, JavaScript, برمجة',
                'excerpt': 'دليل شامل لتعلم تطوير المواقع الإلكترونية من الصفر'
            },
            {
                'title': 'أمان المعلومات في العصر الرقمي',
                'content': '''
                أمان المعلومات أصبح أولوية قصوى في عالمنا الرقمي المترابط. كل يوم نسمع عن اختراقات وهجمات إلكترونية جديدة.

                ## أهمية أمان المعلومات

                حماية البيانات الشخصية والمؤسسية من التهديدات الإلكترونية أمر بالغ الأهمية لضمان الخصوصية والأمان.

                ## أنواع التهديدات

                - **البرمجيات الخبيثة**: الفيروسات وأحصنة طروادة
                - **الهندسة الاجتماعية**: خداع المستخدمين
                - **هجمات الشبكات**: اختراق الأنظمة
                - **سرقة الهوية**: انتحال الشخصية

                ## طرق الحماية

                1. استخدام كلمات مرور قوية
                2. تحديث البرامج باستمرار
                3. استخدام برامج مكافحة الفيروسات
                4. الحذر من الروابط المشبوهة
                5. النسخ الاحتياطي للبيانات

                الأمان مسؤولية الجميع، ويجب أن نكون واعين للمخاطر وطرق الحماية.
                ''',
                'tags': 'أمان معلومات, حماية, تقنية, خصوصية',
                'excerpt': 'تعرف على أهمية أمان المعلومات وطرق حماية بياناتك في العصر الرقمي'
            },
            {
                'title': 'مستقبل التجارة الإلكترونية',
                'content': '''
                التجارة الإلكترونية تشهد نمواً هائلاً، خاصة بعد جائحة كوفيد-19 التي غيرت عادات التسوق للأبد.

                ## الوضع الحالي

                التجارة الإلكترونية تنمو بمعدلات قياسية، مع توقعات بوصولها لتريليونات الدولارات في السنوات القادمة.

                ## الاتجاهات المستقبلية

                ### التسوق بالواقع المعزز
                تجربة المنتجات افتراضياً قبل الشراء

                ### الذكاء الاصطناعي في التوصيات
                توصيات مخصصة لكل عميل

                ### التجارة الصوتية
                التسوق عبر المساعدات الصوتية

                ### التجارة الاجتماعية
                الشراء مباشرة من منصات التواصل

                ## التحديات

                - المنافسة الشديدة
                - أمان المدفوعات
                - لوجستيات التوصيل
                - ثقة العملاء

                ## الفرص

                - الوصول لأسواق عالمية
                - تكاليف أقل من المتاجر التقليدية
                - بيانات العملاء للتحليل
                - المرونة في العمل

                المستقبل مشرق للتجارة الإلكترونية، والمهم هو التكيف مع التطورات الجديدة.
                ''',
                'tags': 'تجارة إلكترونية, تسوق, تقنية, أعمال',
                'excerpt': 'استكشف مستقبل التجارة الإلكترونية والاتجاهات الجديدة في عالم التسوق الرقمي'
            }
        ]
        
        # Create articles
        articles_created = 0
        for i in range(options['articles']):
            # Select random article template
            template = random.choice(sample_articles)
            
            # Create unique title
            title = f"{template['title']} - الجزء {i+1}" if i > 0 else template['title']
            
            article = Article.objects.create(
                title=title,
                content=template['content'],
                excerpt=template['excerpt'],
                tags=template['tags'],
                status=Article.PUBLISHED,
                author=admin_user,
                published_at=timezone.now() - timezone.timedelta(days=random.randint(1, 30)),
                views_count=random.randint(10, 1000)
            )
            
            articles_created += 1
            
            if articles_created % 5 == 0:
                self.stdout.write(f'Created {articles_created} articles...')
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully created {articles_created} articles and admin user!'
            )
        )
        self.stdout.write(
            self.style.WARNING(
                'Admin credentials: username=admin, password=admin123'
            )
        )
