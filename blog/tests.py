from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from django.utils import timezone
from .models import Article


class ArticleModelTest(TestCase):
    """Test Article model functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
    
    def test_article_creation(self):
        """Test creating an article"""
        article = Article.objects.create(
            title='مقال تجريبي',
            content='هذا محتوى المقال التجريبي',
            excerpt='مقتطف من المقال',
            tags='تجربة, اختبار',
            status=Article.PUBLISHED,
            author=self.user
        )
        
        self.assertEqual(article.title, 'مقال تجريبي')
        self.assertEqual(article.author, self.user)
        self.assertEqual(article.status, Article.PUBLISHED)
        self.assertTrue(article.is_published)
    
    def test_article_slug_generation(self):
        """Test automatic slug generation"""
        article = Article.objects.create(
            title='مقال باللغة العربية',
            content='محتوى المقال',
            author=self.user
        )
        
        self.assertIsNotNone(article.slug)
        self.assertTrue(len(article.slug) > 0)
    
    def test_article_excerpt_generation(self):
        """Test automatic excerpt generation"""
        long_content = 'هذا نص طويل جداً ' * 50
        article = Article.objects.create(
            title='مقال بمحتوى طويل',
            content=long_content,
            author=self.user
        )
        
        self.assertIsNotNone(article.excerpt)
        self.assertTrue(len(article.excerpt) <= 203)  # 200 chars + '...'
    
    def test_get_tags_list(self):
        """Test tags list functionality"""
        article = Article.objects.create(
            title='مقال مع كلمات مفتاحية',
            content='محتوى المقال',
            tags='تقنية, برمجة, تطوير',
            author=self.user
        )
        
        tags = article.get_tags_list()
        self.assertEqual(len(tags), 3)
        self.assertIn('تقنية', tags)
        self.assertIn('برمجة', tags)
        self.assertIn('تطوير', tags)
    
    def test_increment_views(self):
        """Test views count increment"""
        article = Article.objects.create(
            title='مقال للاختبار',
            content='محتوى المقال',
            author=self.user
        )
        
        initial_views = article.views_count
        article.increment_views()
        
        self.assertEqual(article.views_count, initial_views + 1)
    
    def test_published_articles_queryset(self):
        """Test published articles queryset"""
        # Create published article
        published_article = Article.objects.create(
            title='مقال منشور',
            content='محتوى المقال المنشور',
            status=Article.PUBLISHED,
            author=self.user,
            published_at=timezone.now()
        )
        
        # Create draft article
        draft_article = Article.objects.create(
            title='مقال مسودة',
            content='محتوى المقال المسودة',
            status=Article.DRAFT,
            author=self.user
        )
        
        published_articles = Article.published_articles()
        
        self.assertIn(published_article, published_articles)
        self.assertNotIn(draft_article, published_articles)


class BlogViewsTest(TestCase):
    """Test blog views functionality"""
    
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create test articles
        self.published_article = Article.objects.create(
            title='مقال منشور للاختبار',
            content='محتوى المقال المنشور',
            excerpt='مقتطف المقال',
            tags='اختبار, تجربة',
            status=Article.PUBLISHED,
            author=self.user,
            published_at=timezone.now()
        )
        
        self.draft_article = Article.objects.create(
            title='مقال مسودة',
            content='محتوى المقال المسودة',
            status=Article.DRAFT,
            author=self.user
        )
    
    def test_home_view(self):
        """Test home page view"""
        response = self.client.get(reverse('blog:home'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.published_article.title)
        self.assertNotContains(response, self.draft_article.title)
    
    def test_article_detail_view(self):
        """Test article detail view"""
        response = self.client.get(
            reverse('blog:article_detail', kwargs={'slug': self.published_article.slug})
        )
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.published_article.title)
        self.assertContains(response, self.published_article.content)
    
    def test_article_detail_view_increments_views(self):
        """Test that article detail view increments views count"""
        initial_views = self.published_article.views_count
        
        self.client.get(
            reverse('blog:article_detail', kwargs={'slug': self.published_article.slug})
        )
        
        self.published_article.refresh_from_db()
        self.assertEqual(self.published_article.views_count, initial_views + 1)
    
    def test_search_view(self):
        """Test search functionality"""
        response = self.client.get(reverse('blog:search'), {'q': 'منشور'})
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.published_article.title)
    
    def test_search_view_empty_query(self):
        """Test search with empty query"""
        response = self.client.get(reverse('blog:search'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'البحث')
    
    def test_tag_view(self):
        """Test tag filtering view"""
        response = self.client.get(reverse('blog:tag', kwargs={'tag': 'اختبار'}))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, self.published_article.title)
    
    def test_about_view(self):
        """Test about page view"""
        response = self.client.get(reverse('blog:about'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'حول المدونة')
    
    def test_contact_view_get(self):
        """Test contact page GET request"""
        response = self.client.get(reverse('blog:contact'))
        
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, 'اتصل بنا')
    
    def test_contact_view_post(self):
        """Test contact form submission"""
        response = self.client.post(reverse('blog:contact'), {
            'name': 'أحمد محمد',
            'email': '<EMAIL>',
            'message': 'رسالة تجريبية'
        })
        
        self.assertEqual(response.status_code, 200)
        # Should return JSON response
        self.assertEqual(response['Content-Type'], 'application/json')


class BlogAdminTest(TestCase):
    """Test blog admin functionality"""
    
    def setUp(self):
        self.admin_user = User.objects.create_superuser(
            username='admin',
            email='<EMAIL>',
            password='adminpass123'
        )
        self.client = Client()
        self.client.login(username='admin', password='adminpass123')
    
    def test_admin_article_list(self):
        """Test admin article list view"""
        response = self.client.get('/admin/blog/article/')
        
        self.assertEqual(response.status_code, 200)
    
    def test_admin_article_add(self):
        """Test adding article through admin"""
        response = self.client.post('/admin/blog/article/add/', {
            'title': 'مقال جديد من الإدارة',
            'slug': 'new-admin-article',
            'content': 'محتوى المقال الجديد',
            'status': Article.PUBLISHED,
            'author': self.admin_user.id,
        })
        
        # Should redirect after successful creation
        self.assertEqual(response.status_code, 302)
        
        # Check if article was created
        self.assertTrue(
            Article.objects.filter(title='مقال جديد من الإدارة').exists()
        )


class RTLSupportTest(TestCase):
    """Test RTL and Arabic support"""
    
    def test_rtl_direction_in_templates(self):
        """Test that templates have RTL direction"""
        response = self.client.get(reverse('blog:home'))
        
        self.assertContains(response, 'dir="rtl"')
        self.assertContains(response, 'lang="ar"')
    
    def test_arabic_font_loading(self):
        """Test that Arabic font is loaded"""
        response = self.client.get(reverse('blog:home'))
        
        self.assertContains(response, 'IBM Plex Sans Arabic')
    
    def test_rtl_css_loading(self):
        """Test that RTL CSS files are loaded"""
        response = self.client.get(reverse('blog:home'))
        
        self.assertContains(response, 'rtl-arabic.css')
        self.assertContains(response, 'responsive-mobile.css')


class SEOTest(TestCase):
    """Test SEO functionality"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        self.article = Article.objects.create(
            title='مقال محسن لمحركات البحث',
            content='محتوى المقال مع كلمات مفتاحية مهمة',
            excerpt='مقتطف محسن لمحركات البحث',
            tags='SEO, تحسين, محركات بحث',
            status=Article.PUBLISHED,
            author=self.user,
            published_at=timezone.now()
        )
    
    def test_meta_tags_in_home(self):
        """Test meta tags in home page"""
        response = self.client.get(reverse('blog:home'))
        
        self.assertContains(response, '<meta name="description"')
        self.assertContains(response, '<meta name="keywords"')
        self.assertContains(response, '<meta property="og:title"')
    
    def test_meta_tags_in_article_detail(self):
        """Test meta tags in article detail page"""
        response = self.client.get(
            reverse('blog:article_detail', kwargs={'slug': self.article.slug})
        )
        
        self.assertContains(response, self.article.title)
        self.assertContains(response, self.article.excerpt)
        self.assertContains(response, '<meta name="description"')
    
    def test_structured_data(self):
        """Test that pages have proper HTML structure"""
        response = self.client.get(reverse('blog:home'))
        
        self.assertContains(response, '<main')
        self.assertContains(response, '<article')
        self.assertContains(response, '<header')
        self.assertContains(response, '<footer')
