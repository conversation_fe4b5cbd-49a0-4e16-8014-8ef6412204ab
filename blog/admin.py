from django.contrib import admin
from django.utils.html import format_html
from django.urls import reverse
from django.utils import timezone
from .models import Article


@admin.register(Article)
class ArticleAdmin(admin.ModelAdmin):
    """
    Admin configuration for Article model with Arabic interface
    """
    list_display = [
        'title',
        'status_badge',
        'author',
        'views_count',
        'created_at',
        'published_at',
        'actions_column'
    ]
    
    list_filter = [
        'status',
        'created_at',
        'published_at',
        'author'
    ]
    
    search_fields = [
        'title',
        'content',
        'tags'
    ]
    
    prepopulated_fields = {
        'slug': ('title',)
    }
    
    fieldsets = (
        ('معلومات أساسية', {
            'fields': ('title', 'slug', 'author', 'status')
        }),
        ('المحتوى', {
            'fields': ('content', 'excerpt', 'image')
        }),
        ('إعدادات إضافية', {
            'fields': ('tags', 'published_at'),
            'classes': ('collapse',)
        }),
        ('إحصائيات', {
            'fields': ('views_count', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    readonly_fields = [
        'created_at',
        'updated_at',
        'views_count'
    ]
    
    list_per_page = 20
    date_hierarchy = 'created_at'
    
    def status_badge(self, obj):
        """Display status as a colored badge"""
        if obj.status == Article.PUBLISHED:
            color = 'green'
            text = 'منشور'
        else:
            color = 'orange'
            text = 'مسودة'
        
        return format_html(
            '<span style="color: {}; font-weight: bold;">{}</span>',
            color,
            text
        )
    status_badge.short_description = 'حالة النشر'
    
    def actions_column(self, obj):
        """Display action buttons"""
        view_url = obj.get_absolute_url()
        edit_url = reverse('admin:blog_article_change', args=[obj.pk])
        
        return format_html(
            '<a href="{}" target="_blank" style="margin-left: 10px;">عرض</a>'
            '<a href="{}">تعديل</a>',
            view_url,
            edit_url
        )
    actions_column.short_description = 'إجراءات'
    
    def save_model(self, request, obj, form, change):
        """Auto-set author and published_at when saving"""
        if not change:  # If creating new article
            obj.author = request.user
        
        # Set published_at when status changes to published
        if obj.status == Article.PUBLISHED and not obj.published_at:
            obj.published_at = timezone.now()
        
        super().save_model(request, obj, form, change)
    
    def get_queryset(self, request):
        """Optimize queryset with select_related"""
        return super().get_queryset(request).select_related('author')
    
    class Media:
        css = {
            'all': ('admin/css/rtl.css',)
        }


# Customize admin site headers
admin.site.site_header = 'إدارة المدونة العربية'
admin.site.site_title = 'إدارة المدونة'
admin.site.index_title = 'لوحة التحكم'
