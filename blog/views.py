from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
from django.http import JsonResponse
from django.views.generic import ListView, DetailView
from .models import Article


class HomeView(ListView):
    """
    Home page view displaying published articles
    """
    model = Article
    template_name = 'blog/home.html'
    context_object_name = 'articles'
    paginate_by = 6
    
    def get_queryset(self):
        return Article.published_articles()
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = 'الرئيسية'
        context['featured_article'] = Article.published_articles().first()
        return context


class ArticleDetailView(DetailView):
    """
    Article detail view
    """
    model = Article
    template_name = 'blog/article_detail.html'
    context_object_name = 'article'
    slug_field = 'slug'
    slug_url_kwarg = 'slug'
    
    def get_queryset(self):
        return Article.published_articles()
    
    def get_object(self, queryset=None):
        obj = super().get_object(queryset)
        # Increment views count
        obj.increment_views()
        return obj
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['page_title'] = self.object.title
        context['related_articles'] = Article.published_articles().exclude(
            id=self.object.id
        )[:3]
        return context


def search_view(request):
    """
    Search functionality for articles
    """
    query = request.GET.get('q', '')
    articles = []
    
    if query:
        articles = Article.published_articles().filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(tags__icontains=query)
        ).distinct()
    
    # Pagination
    paginator = Paginator(articles, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'articles': page_obj,
        'query': query,
        'page_title': f'البحث: {query}' if query else 'البحث',
        'total_results': articles.count() if query else 0
    }
    
    return render(request, 'blog/search.html', context)


def tag_view(request, tag):
    """
    View articles by tag
    """
    articles = Article.published_articles().filter(
        tags__icontains=tag
    )
    
    # Pagination
    paginator = Paginator(articles, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'articles': page_obj,
        'tag': tag,
        'page_title': f'الكلمة المفتاحية: {tag}',
        'total_results': articles.count()
    }
    
    return render(request, 'blog/tag.html', context)


def about_view(request):
    """
    About page view
    """
    context = {
        'page_title': 'حول المدونة'
    }
    return render(request, 'blog/about.html', context)


def contact_view(request):
    """
    Contact page view
    """
    if request.method == 'POST':
        # Handle contact form submission
        name = request.POST.get('name')
        email = request.POST.get('email')
        message = request.POST.get('message')
        
        # Here you would typically send an email or save to database
        # For now, we'll just return a success response
        
        return JsonResponse({
            'success': True,
            'message': 'تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.'
        })
    
    context = {
        'page_title': 'اتصل بنا'
    }
    return render(request, 'blog/contact.html', context)


# Function-based views for simpler implementation
def home_view(request):
    """
    Home page view - function-based alternative
    """
    articles = Article.published_articles()
    featured_article = articles.first()
    
    # Pagination
    paginator = Paginator(articles, 6)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'articles': page_obj,
        'featured_article': featured_article,
        'page_title': 'الرئيسية'
    }
    
    return render(request, 'blog/home.html', context)


def article_detail_view(request, slug):
    """
    Article detail view - function-based alternative
    """
    article = get_object_or_404(Article.published_articles(), slug=slug)
    
    # Increment views count
    article.increment_views()
    
    # Get related articles
    related_articles = Article.published_articles().exclude(
        id=article.id
    )[:3]
    
    context = {
        'article': article,
        'related_articles': related_articles,
        'page_title': article.title
    }
    
    return render(request, 'blog/article_detail.html', context)
