from django.db import models
from django.urls import reverse
from django.utils import timezone
from django.utils.text import slugify
from django.contrib.auth.models import User


class Article(models.Model):
    """
    Article model for the Arabic blog system
    """
    DRAFT = 'draft'
    PUBLISHED = 'published'
    
    STATUS_CHOICES = [
        (DRAFT, 'مسودة'),
        (PUBLISHED, 'منشور'),
    ]
    
    title = models.CharField(
        max_length=200,
        verbose_name='عنوان المقال',
        help_text='عنوان المقال باللغة العربية'
    )
    
    slug = models.SlugField(
        max_length=200,
        unique=True,
        verbose_name='الرابط',
        help_text='رابط المقال (يتم إنشاؤه تلقائياً)'
    )
    
    content = models.TextField(
        verbose_name='نص المقال',
        help_text='محتوى المقال الكامل'
    )
    
    excerpt = models.TextField(
        max_length=300,
        blank=True,
        verbose_name='مقتطف',
        help_text='مقتطف قصير من المقال (اختياري)'
    )
    
    image = models.ImageField(
        upload_to='articles/',
        blank=True,
        null=True,
        verbose_name='صورة',
        help_text='صورة المقال (اختياري)'
    )
    
    tags = models.CharField(
        max_length=200,
        blank=True,
        verbose_name='الكلمات المفتاحية',
        help_text='الكلمات المفتاحية مفصولة بفواصل'
    )
    
    status = models.CharField(
        max_length=10,
        choices=STATUS_CHOICES,
        default=DRAFT,
        verbose_name='حالة النشر'
    )
    
    author = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name='الكاتب'
    )
    
    created_at = models.DateTimeField(
        auto_now_add=True,
        verbose_name='تاريخ الإنشاء'
    )
    
    updated_at = models.DateTimeField(
        auto_now=True,
        verbose_name='تاريخ التحديث'
    )
    
    published_at = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name='تاريخ النشر'
    )
    
    views_count = models.PositiveIntegerField(
        default=0,
        verbose_name='عدد المشاهدات'
    )
    
    class Meta:
        verbose_name = 'مقال'
        verbose_name_plural = 'المقالات'
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        # Auto-generate slug from title if not provided
        if not self.slug:
            self.slug = slugify(self.title, allow_unicode=True)
        
        # Set published_at when status changes to published
        if self.status == self.PUBLISHED and not self.published_at:
            self.published_at = timezone.now()
        
        # Generate excerpt from content if not provided
        if not self.excerpt and self.content:
            self.excerpt = self.content[:200] + '...' if len(self.content) > 200 else self.content
        
        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        return reverse('blog:article_detail', kwargs={'slug': self.slug})
    
    def get_tags_list(self):
        """Return tags as a list"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',')]
        return []
    
    def increment_views(self):
        """Increment the views count"""
        self.views_count += 1
        self.save(update_fields=['views_count'])
    
    @property
    def is_published(self):
        return self.status == self.PUBLISHED
    
    @classmethod
    def published_articles(cls):
        """Return only published articles"""
        return cls.objects.filter(status=cls.PUBLISHED).order_by('-published_at')
