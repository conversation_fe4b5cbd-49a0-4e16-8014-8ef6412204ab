# المدونة العربية - Arabic Blog System

نظام مدونة عربية متكامل مبني بـ Django و Tailwind CSS مع دعم كامل للـ RTL والخط العربي.

## المميزات

### 🌟 المميزات الأساسية
- **دعم كامل للغة العربية**: RTL، خط IBM Plex Sans Arabic، تخطيط مناسب للنصوص العربية
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة (هاتف، تابلت، كمبيوتر)
- **لوحة تحكم عربية**: إدارة المقالات بواجهة عربية سهلة الاستخدام
- **بحث متقدم**: بحث في العناوين والمحتوى والكلمات المفتاحية
- **نظام الكلمات المفتاحية**: تصنيف وفلترة المقالات
- **عداد المشاهدات**: تتبع شعبية المقالات

### 🎨 التصميم والواجهة
- **Tailwind CSS**: تصميم حديث ومرن
- **تحسين الأداء**: تحميل سريع وتحسين للصور
- **تجربة مستخدم ممتازة**: تنقل سهل وواضح
- **دعم الوضع المظلم**: تلقائي حسب إعدادات النظام
- **أيقونات Font Awesome**: أيقونات جميلة ومعبرة

### 📱 التحسين للهواتف المحمولة
- **Mobile-First Design**: مصمم أولاً للهواتف المحمولة
- **قائمة تنقل متجاوبة**: قائمة هامبرغر للهواتف
- **لمسات محسنة**: أهداف لمس مناسبة للأصابع
- **تحسين الأداء**: تحميل مُحسن للهواتف

### 🔧 المميزات التقنية
- **Django 4.2**: إطار عمل قوي وآمن
- **SQLite**: قاعدة بيانات سهلة الإعداد
- **إدارة الصور**: رفع وعرض الصور مع التحسين
- **SEO محسن**: علامات meta وهيكل مناسب لمحركات البحث
- **أمان عالي**: حماية من CSRF وXSS

## متطلبات النظام

- Python 3.9+
- Django 4.2+
- Pillow (لمعالجة الصور)

## التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone <repository-url>
cd P
```

### 2. إنشاء البيئة الافتراضية
```bash
python3 -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate     # على Windows
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد قاعدة البيانات
```bash
python manage.py makemigrations
python manage.py migrate
```

### 5. إنشاء بيانات تجريبية (اختياري)
```bash
python manage.py create_sample_data --articles 20
```

### 6. تشغيل الخادم
```bash
python manage.py runserver
```

### 7. الوصول للموقع
- الموقع الرئيسي: http://127.0.0.1:8000/
- لوحة التحكم: http://127.0.0.1:8000/admin/
- بيانات الدخول الافتراضية: `admin` / `admin123`

## هيكل المشروع

```
P/
├── arabic_blog/          # إعدادات Django الرئيسية
├── blog/                 # تطبيق المدونة
│   ├── models.py        # نماذج البيانات
│   ├── views.py         # منطق العرض
│   ├── admin.py         # إعدادات لوحة التحكم
│   ├── urls.py          # روابط التطبيق
│   └── management/      # أوامر إدارية مخصصة
├── templates/           # قوالب HTML
│   ├── base.html       # القالب الأساسي
│   └── blog/           # قوالب المدونة
├── static/             # الملفات الثابتة
│   ├── css/           # ملفات CSS
│   └── js/            # ملفات JavaScript
├── media/             # ملفات المستخدمين المرفوعة
├── requirements.txt   # متطلبات Python
└── README.md         # هذا الملف
```

## الاستخدام

### إضافة مقال جديد
1. اذهب إلى لوحة التحكم: `/admin/`
2. سجل دخولك
3. اضغط على "المقالات" ثم "إضافة مقال"
4. املأ البيانات المطلوبة
5. اختر حالة النشر واحفظ

### تخصيص التصميم
- عدل ملفات CSS في مجلد `static/css/`
- استخدم Tailwind CSS classes في القوالب
- الألوان الأساسية محددة في `base.html`

### إضافة صفحات جديدة
1. أضف view جديد في `blog/views.py`
2. أضف URL في `blog/urls.py`
3. أنشئ قالب HTML في `templates/blog/`

## التخصيص

### تغيير الألوان
عدل المتغيرات في `base.html`:
```javascript
colors: {
    'primary': '#00BFA6',    // اللون الأساسي
    'secondary': '#0F0F0F',  // اللون الثانوي
    'accent': '#FF6B35',     // لون التمييز
    'gold': '#FFD700',       // اللون الذهبي
}
```

### إضافة خطوط جديدة
1. أضف رابط الخط في `base.html`
2. عدل `font-family` في CSS
3. تأكد من دعم الخط للعربية

### تخصيص لوحة التحكم
عدل `blog/admin.py` لتخصيص:
- الحقول المعروضة
- الفلاتر
- البحث
- التجميع

## الأمان

### إعدادات الإنتاج
قبل النشر، تأكد من:
- تغيير `SECRET_KEY`
- تعيين `DEBUG = False`
- إضافة النطاق إلى `ALLOWED_HOSTS`
- استخدام قاعدة بيانات إنتاج (PostgreSQL)
- إعداد HTTPS

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
python manage.py dumpdata > backup.json

# استعادة النسخة الاحتياطية
python manage.py loaddata backup.json
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. إضافة التحسينات
4. إرسال Pull Request

## الدعم

للحصول على المساعدة:
- افتح Issue في GitHub
- راسلنا على: <EMAIL>
- تابعنا على وسائل التواصل الاجتماعي

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف LICENSE للتفاصيل.

## الشكر والتقدير

- Django Framework
- Tailwind CSS
- Font Awesome
- IBM Plex Sans Arabic Font
- المجتمع العربي للمطورين

---

**المدونة العربية** - نظام مدونة عربي حديث ومتكامل 🚀
