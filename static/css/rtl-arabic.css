/* RTL Arabic Blog Styles */
/* Enhanced RTL support and Arabic typography */

/* Base RTL Configuration */
html[dir="rtl"] {
    direction: rtl;
    text-align: right;
}

body {
    font-family: 'IBM Plex Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    font-feature-settings: "liga" 1, "kern" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Arabic Text Optimization */
.arabic-text {
    font-family: 'IBM Plex Sans Arabic', sans-serif;
    line-height: 1.8;
    letter-spacing: 0.02em;
    word-spacing: 0.1em;
}

/* RTL Flexbox Adjustments */
.rtl-flex {
    display: flex;
    flex-direction: row-reverse;
}

.rtl-flex-col {
    display: flex;
    flex-direction: column;
}

/* RTL Grid Adjustments */
.rtl-grid {
    direction: rtl;
}

/* RTL Text Alignment */
.text-start-rtl {
    text-align: right;
}

.text-end-rtl {
    text-align: left;
}

/* RTL Margin and Padding Utilities */
.mr-rtl-2 { margin-right: 0.5rem; }
.mr-rtl-4 { margin-right: 1rem; }
.mr-rtl-6 { margin-right: 1.5rem; }
.mr-rtl-8 { margin-right: 2rem; }

.ml-rtl-2 { margin-left: 0.5rem; }
.ml-rtl-4 { margin-left: 1rem; }
.ml-rtl-6 { margin-left: 1.5rem; }
.ml-rtl-8 { margin-left: 2rem; }

.pr-rtl-2 { padding-right: 0.5rem; }
.pr-rtl-4 { padding-right: 1rem; }
.pr-rtl-6 { padding-right: 1.5rem; }
.pr-rtl-8 { padding-right: 2rem; }

.pl-rtl-2 { padding-left: 0.5rem; }
.pl-rtl-4 { padding-left: 1rem; }
.pl-rtl-6 { padding-left: 1.5rem; }
.pl-rtl-8 { padding-left: 2rem; }

/* RTL Border Utilities */
.border-r-rtl { border-right: 1px solid #e5e7eb; }
.border-l-rtl { border-left: 1px solid #e5e7eb; }
.border-r-rtl-2 { border-right: 2px solid #00BFA6; }
.border-l-rtl-2 { border-left: 2px solid #00BFA6; }

/* Arabic Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-family: 'IBM Plex Sans Arabic', sans-serif;
    font-weight: 700;
    line-height: 1.4;
    color: #1f2937;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

/* Paragraph Styling for Arabic */
p {
    font-family: 'IBM Plex Sans Arabic', sans-serif;
    line-height: 1.8;
    margin-bottom: 1rem;
    text-align: justify;
    hyphens: auto;
    word-wrap: break-word;
}

/* List Styling for Arabic */
ul, ol {
    padding-right: 1.5rem;
    padding-left: 0;
}

ul li::marker {
    content: "• ";
    color: #00BFA6;
}

ol li::marker {
    color: #00BFA6;
    font-weight: bold;
}

/* Quote Styling for Arabic */
blockquote {
    border-right: 4px solid #00BFA6;
    border-left: none;
    padding-right: 1rem;
    padding-left: 0;
    margin: 1.5rem 0;
    font-style: italic;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    position: relative;
}

blockquote::before {
    content: """;
    font-size: 4rem;
    color: #00BFA6;
    position: absolute;
    top: -10px;
    right: 10px;
    font-family: serif;
}

/* Form Elements RTL */
input[type="text"],
input[type="email"],
input[type="password"],
input[type="search"],
textarea,
select {
    direction: rtl;
    text-align: right;
    font-family: 'IBM Plex Sans Arabic', sans-serif;
}

input::placeholder,
textarea::placeholder {
    text-align: right;
    direction: rtl;
}

/* Button Styling for Arabic */
button,
.btn {
    font-family: 'IBM Plex Sans Arabic', sans-serif;
    font-weight: 500;
}

/* Navigation RTL Adjustments */
.nav-rtl {
    direction: rtl;
}

.nav-rtl .nav-item {
    margin-left: 1rem;
    margin-right: 0;
}

/* Card Components RTL */
.card-rtl {
    direction: rtl;
    text-align: right;
}

.card-rtl .card-header {
    text-align: right;
}

.card-rtl .card-body {
    text-align: right;
}

/* Table RTL Support */
table {
    direction: rtl;
}

th, td {
    text-align: right;
}

/* Breadcrumb RTL */
.breadcrumb-rtl {
    direction: rtl;
}

.breadcrumb-rtl .breadcrumb-separator {
    transform: scaleX(-1);
}

/* Pagination RTL */
.pagination-rtl {
    direction: rtl;
}

.pagination-rtl .page-link {
    margin-left: 0.25rem;
    margin-right: 0;
}

/* Modal RTL */
.modal-rtl {
    direction: rtl;
    text-align: right;
}

.modal-rtl .modal-header {
    text-align: right;
}

.modal-rtl .modal-body {
    text-align: right;
}

/* Dropdown RTL */
.dropdown-rtl {
    direction: rtl;
}

.dropdown-rtl .dropdown-menu {
    right: 0;
    left: auto;
    text-align: right;
}

/* Alert RTL */
.alert-rtl {
    direction: rtl;
    text-align: right;
}

/* Badge RTL */
.badge-rtl {
    direction: rtl;
}

/* Progress Bar RTL */
.progress-rtl {
    direction: rtl;
}

.progress-rtl .progress-bar {
    right: 0;
    left: auto;
}

/* Tooltip RTL */
.tooltip-rtl {
    direction: rtl;
}

/* Responsive RTL Adjustments */
@media (max-width: 768px) {
    .mobile-rtl {
        direction: rtl;
        text-align: right;
    }
    
    .mobile-nav-rtl {
        direction: rtl;
    }
    
    .mobile-nav-rtl .nav-item {
        text-align: right;
        margin-right: 0;
        margin-left: 1rem;
    }
}

/* Print Styles for Arabic */
@media print {
    body {
        font-family: 'IBM Plex Sans Arabic', serif;
        direction: rtl;
        text-align: right;
    }
    
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid;
    }
    
    p {
        orphans: 3;
        widows: 3;
    }
}

/* Animation Adjustments for RTL */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-rtl {
    animation: slideInRight 0.5s ease-out;
}

.slide-in-ltr {
    animation: slideInLeft 0.5s ease-out;
}

/* Custom Scrollbar for RTL */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #00BFA6;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #00A693;
}

/* Firefox Scrollbar */
html {
    scrollbar-width: thin;
    scrollbar-color: #00BFA6 #f1f1f1;
}

/* Selection Color */
::selection {
    background-color: #00BFA6;
    color: white;
}

::-moz-selection {
    background-color: #00BFA6;
    color: white;
}
