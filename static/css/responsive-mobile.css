/* Responsive Mobile Optimization for Arabic Blog */
/* Mobile-first approach with RTL support */

/* Base Mobile Styles (320px and up) */
@media screen and (min-width: 320px) {
    body {
        font-size: 16px;
        line-height: 1.6;
    }
    
    .container-mobile {
        padding: 0 1rem;
        max-width: 100%;
    }
    
    /* Mobile Navigation */
    .mobile-nav {
        display: block;
    }
    
    .desktop-nav {
        display: none;
    }
    
    /* Mobile Typography */
    h1 { font-size: 1.875rem; } /* 30px */
    h2 { font-size: 1.5rem; }   /* 24px */
    h3 { font-size: 1.25rem; }  /* 20px */
    h4 { font-size: 1.125rem; } /* 18px */
    
    /* Mobile Article Cards */
    .article-card-mobile {
        margin-bottom: 1.5rem;
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .article-card-mobile img {
        height: 200px;
        object-fit: cover;
    }
    
    .article-card-mobile .card-content {
        padding: 1rem;
    }
    
    /* Mobile Search */
    .search-mobile {
        width: 100%;
        margin-bottom: 1rem;
    }
    
    .search-mobile input {
        width: 100%;
        padding: 0.75rem;
        font-size: 1rem;
    }
    
    /* Mobile Buttons */
    .btn-mobile {
        width: 100%;
        padding: 0.875rem 1rem;
        font-size: 1rem;
        margin-bottom: 0.5rem;
    }
    
    /* Mobile Forms */
    .form-mobile input,
    .form-mobile textarea,
    .form-mobile select {
        width: 100%;
        padding: 0.875rem;
        font-size: 1rem;
        margin-bottom: 1rem;
    }
    
    /* Mobile Tables */
    .table-mobile {
        display: block;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .table-mobile table {
        min-width: 600px;
    }
}

/* Small Mobile (375px and up) */
@media screen and (min-width: 375px) {
    .container-mobile {
        padding: 0 1.25rem;
    }
    
    h1 { font-size: 2rem; } /* 32px */
    h2 { font-size: 1.625rem; } /* 26px */
    
    .article-card-mobile img {
        height: 220px;
    }
    
    .article-card-mobile .card-content {
        padding: 1.25rem;
    }
}

/* Large Mobile (414px and up) */
@media screen and (min-width: 414px) {
    .container-mobile {
        padding: 0 1.5rem;
    }
    
    h1 { font-size: 2.25rem; } /* 36px */
    h2 { font-size: 1.75rem; } /* 28px */
    
    .article-card-mobile img {
        height: 240px;
    }
    
    /* Mobile Grid - 2 columns for larger phones */
    .grid-mobile-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1rem;
    }
}

/* Tablet Portrait (768px and up) */
@media screen and (min-width: 768px) {
    .desktop-nav {
        display: block;
    }
    
    .mobile-nav {
        display: none;
    }
    
    .container-mobile {
        padding: 0 2rem;
        max-width: 768px;
        margin: 0 auto;
    }
    
    /* Tablet Typography */
    h1 { font-size: 2.5rem; } /* 40px */
    h2 { font-size: 2rem; }   /* 32px */
    h3 { font-size: 1.5rem; } /* 24px */
    
    /* Tablet Grid - 2 columns */
    .grid-tablet-2 {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
    }
    
    /* Tablet Grid - 3 columns */
    .grid-tablet-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1.5rem;
    }
    
    .article-card-mobile {
        margin-bottom: 2rem;
    }
    
    .article-card-mobile img {
        height: 200px;
    }
    
    /* Tablet Forms */
    .form-tablet {
        max-width: 500px;
        margin: 0 auto;
    }
    
    .btn-mobile {
        width: auto;
        display: inline-block;
        margin-left: 0.5rem;
    }
}

/* Tablet Landscape (1024px and up) */
@media screen and (min-width: 1024px) {
    .container-mobile {
        max-width: 1024px;
        padding: 0 2.5rem;
    }
    
    /* Desktop Typography */
    h1 { font-size: 3rem; }   /* 48px */
    h2 { font-size: 2.25rem; } /* 36px */
    h3 { font-size: 1.75rem; } /* 28px */
    
    /* Desktop Grid - 3 columns */
    .grid-desktop-3 {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
    }
    
    /* Desktop Grid - 4 columns */
    .grid-desktop-4 {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 2rem;
    }
    
    .article-card-mobile img {
        height: 240px;
    }
    
    /* Desktop Forms */
    .form-desktop {
        max-width: 600px;
        margin: 0 auto;
    }
}

/* Large Desktop (1280px and up) */
@media screen and (min-width: 1280px) {
    .container-mobile {
        max-width: 1280px;
        padding: 0 3rem;
    }
    
    h1 { font-size: 3.5rem; } /* 56px */
    h2 { font-size: 2.5rem; } /* 40px */
    
    .article-card-mobile img {
        height: 280px;
    }
}

/* Extra Large Desktop (1536px and up) */
@media screen and (min-width: 1536px) {
    .container-mobile {
        max-width: 1536px;
        padding: 0 4rem;
    }
    
    h1 { font-size: 4rem; } /* 64px */
    h2 { font-size: 3rem; } /* 48px */
}

/* Mobile-Specific RTL Adjustments */
@media screen and (max-width: 767px) {
    /* Mobile Navigation RTL */
    .mobile-menu-rtl {
        direction: rtl;
        text-align: right;
    }
    
    .mobile-menu-rtl .menu-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #e5e7eb;
    }
    
    /* Mobile Search RTL */
    .search-mobile-rtl {
        direction: rtl;
    }
    
    .search-mobile-rtl input {
        text-align: right;
        padding-right: 1rem;
        padding-left: 3rem;
    }
    
    .search-mobile-rtl button {
        right: 0.5rem;
        left: auto;
    }
    
    /* Mobile Article Cards RTL */
    .article-card-mobile-rtl {
        direction: rtl;
        text-align: right;
    }
    
    .article-card-mobile-rtl .card-meta {
        justify-content: flex-start;
    }
    
    .article-card-mobile-rtl .card-meta span {
        margin-left: 1rem;
        margin-right: 0;
    }
    
    /* Mobile Pagination RTL */
    .pagination-mobile-rtl {
        direction: rtl;
        justify-content: center;
    }
    
    .pagination-mobile-rtl .page-link {
        margin-left: 0.25rem;
        margin-right: 0;
    }
    
    /* Mobile Breadcrumb RTL */
    .breadcrumb-mobile-rtl {
        direction: rtl;
        overflow-x: auto;
        white-space: nowrap;
    }
    
    .breadcrumb-mobile-rtl .breadcrumb-item {
        display: inline-block;
        margin-left: 0.5rem;
        margin-right: 0;
    }
}

/* Touch-Friendly Interactions */
@media (hover: none) and (pointer: coarse) {
    /* Increase touch targets */
    button,
    .btn,
    a {
        min-height: 44px;
        min-width: 44px;
        padding: 0.75rem 1rem;
    }
    
    /* Remove hover effects on touch devices */
    .btn-hover:hover {
        transform: none;
        box-shadow: none;
    }
    
    .article-card:hover {
        transform: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    
    /* Improve tap highlighting */
    button,
    .btn,
    a {
        -webkit-tap-highlight-color: rgba(0, 191, 166, 0.3);
    }
}

/* Print Styles */
@media print {
    .mobile-nav,
    .desktop-nav,
    .search-mobile,
    .btn,
    button {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.5;
        color: #000;
        background: #fff;
    }
    
    .article-card-mobile {
        break-inside: avoid;
        margin-bottom: 1rem;
        border: 1px solid #ccc;
        padding: 1rem;
    }
    
    .article-card-mobile img {
        max-width: 100%;
        height: auto;
    }
    
    h1, h2, h3, h4, h5, h6 {
        break-after: avoid;
        color: #000;
    }
    
    p {
        orphans: 3;
        widows: 3;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .article-card-mobile {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
    
    a {
        text-decoration: underline;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    body {
        background-color: #1a1a1a;
        color: #e5e5e5;
    }
    
    .article-card-mobile {
        background-color: #2d2d2d;
        border-color: #404040;
    }
    
    .btn {
        background-color: #00BFA6;
        color: #fff;
    }
    
    input,
    textarea,
    select {
        background-color: #2d2d2d;
        color: #e5e5e5;
        border-color: #404040;
    }
}
