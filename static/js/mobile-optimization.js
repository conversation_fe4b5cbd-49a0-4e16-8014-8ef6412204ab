/**
 * Mobile Optimization JavaScript
 * Enhanced mobile experience for Arabic RTL blog
 */

document.addEventListener('DOMContentLoaded', function() {
    initMobileOptimization();
    initTouchGestures();
    initMobileNavigation();
    initMobileSearch();
    initMobilePerformance();
    initMobileAccessibility();
});

/**
 * Initialize mobile optimization
 */
function initMobileOptimization() {
    // Detect mobile device
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    
    if (isMobile) {
        document.body.classList.add('mobile-device');
        
        // Optimize viewport for mobile
        optimizeViewport();
        
        // Initialize lazy loading for mobile
        initLazyLoading();
        
        // Optimize images for mobile
        optimizeImagesForMobile();
        
        // Initialize mobile-specific features
        initMobileFeatures();
    }
    
    // Handle orientation changes
    window.addEventListener('orientationchange', handleOrientationChange);
    
    // Handle resize events
    let resizeTimer;
    window.addEventListener('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(handleResize, 250);
    });
}

/**
 * Optimize viewport for mobile
 */
function optimizeViewport() {
    // Prevent zoom on input focus (iOS)
    const inputs = document.querySelectorAll('input, textarea, select');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            const viewport = document.querySelector('meta[name="viewport"]');
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no');
        });
        
        input.addEventListener('blur', function() {
            const viewport = document.querySelector('meta[name="viewport"]');
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
        });
    });
}

/**
 * Initialize lazy loading for mobile performance
 */
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for older browsers
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

/**
 * Optimize images for mobile
 */
function optimizeImagesForMobile() {
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
        // Add loading="lazy" for native lazy loading
        img.setAttribute('loading', 'lazy');
        
        // Optimize image sizes for mobile
        if (window.innerWidth <= 768) {
            const src = img.src;
            if (src && !src.includes('mobile-optimized')) {
                // You can implement server-side image optimization here
                // img.src = src.replace(/\.(jpg|jpeg|png)/, '-mobile.$1');
            }
        }
    });
}

/**
 * Initialize touch gestures
 */
function initTouchGestures() {
    let startX, startY, endX, endY;
    
    // Swipe gestures for article navigation
    const articleContent = document.querySelector('.article-content');
    if (articleContent) {
        articleContent.addEventListener('touchstart', function(e) {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        articleContent.addEventListener('touchend', function(e) {
            endX = e.changedTouches[0].clientX;
            endY = e.changedTouches[0].clientY;
            
            handleSwipe();
        });
    }
    
    function handleSwipe() {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        
        // Minimum swipe distance
        const minSwipeDistance = 50;
        
        if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
            if (deltaX > 0) {
                // Swipe right (previous in RTL)
                navigateToPrevious();
            } else {
                // Swipe left (next in RTL)
                navigateToNext();
            }
        }
    }
    
    function navigateToPrevious() {
        const prevLink = document.querySelector('.pagination .previous');
        if (prevLink) {
            window.location.href = prevLink.href;
        }
    }
    
    function navigateToNext() {
        const nextLink = document.querySelector('.pagination .next');
        if (nextLink) {
            window.location.href = nextLink.href;
        }
    }
}

/**
 * Initialize mobile navigation
 */
function initMobileNavigation() {
    const mobileMenuButton = document.querySelector('.mobile-menu-button');
    const mobileMenu = document.querySelector('.mobile-menu');
    const body = document.body;
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            const isOpen = !mobileMenu.classList.contains('hidden');
            
            if (isOpen) {
                closeMobileMenu();
            } else {
                openMobileMenu();
            }
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!mobileMenu.contains(e.target) && !mobileMenuButton.contains(e.target)) {
                closeMobileMenu();
            }
        });
        
        // Close menu on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeMobileMenu();
            }
        });
    }
    
    function openMobileMenu() {
        mobileMenu.classList.remove('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'true');
        body.classList.add('menu-open');
        
        // Focus first menu item
        const firstMenuItem = mobileMenu.querySelector('a');
        if (firstMenuItem) {
            firstMenuItem.focus();
        }
    }
    
    function closeMobileMenu() {
        mobileMenu.classList.add('hidden');
        mobileMenuButton.setAttribute('aria-expanded', 'false');
        body.classList.remove('menu-open');
        mobileMenuButton.focus();
    }
}

/**
 * Initialize mobile search
 */
function initMobileSearch() {
    const searchInput = document.querySelector('input[name="q"]');
    const searchForm = document.querySelector('form[action*="search"]');
    
    if (searchInput && searchForm) {
        // Auto-expand search on mobile
        searchInput.addEventListener('focus', function() {
            if (window.innerWidth <= 768) {
                this.parentElement.classList.add('expanded');
            }
        });
        
        searchInput.addEventListener('blur', function() {
            if (window.innerWidth <= 768 && !this.value) {
                this.parentElement.classList.remove('expanded');
            }
        });
        
        // Voice search support (if available)
        if ('webkitSpeechRecognition' in window) {
            addVoiceSearch(searchInput);
        }
    }
}

/**
 * Add voice search functionality
 */
function addVoiceSearch(searchInput) {
    const voiceButton = document.createElement('button');
    voiceButton.type = 'button';
    voiceButton.className = 'voice-search-btn';
    voiceButton.innerHTML = '<i class="fas fa-microphone"></i>';
    voiceButton.setAttribute('aria-label', 'البحث الصوتي');
    
    searchInput.parentElement.appendChild(voiceButton);
    
    const recognition = new webkitSpeechRecognition();
    recognition.lang = 'ar-SA';
    recognition.continuous = false;
    recognition.interimResults = false;
    
    voiceButton.addEventListener('click', function() {
        recognition.start();
        this.classList.add('listening');
    });
    
    recognition.onresult = function(event) {
        const transcript = event.results[0][0].transcript;
        searchInput.value = transcript;
        voiceButton.classList.remove('listening');
    };
    
    recognition.onerror = function() {
        voiceButton.classList.remove('listening');
    };
}

/**
 * Initialize mobile performance optimizations
 */
function initMobilePerformance() {
    // Debounce scroll events
    let scrollTimer;
    window.addEventListener('scroll', function() {
        clearTimeout(scrollTimer);
        scrollTimer = setTimeout(handleScroll, 16); // ~60fps
    });
    
    // Optimize animations for mobile
    if (window.innerWidth <= 768) {
        const animatedElements = document.querySelectorAll('.animate-on-scroll');
        animatedElements.forEach(element => {
            element.classList.add('mobile-optimized');
        });
    }
    
    // Preload critical resources
    preloadCriticalResources();
}

/**
 * Handle scroll events (debounced)
 */
function handleScroll() {
    // Show/hide mobile header on scroll
    const header = document.querySelector('nav');
    const scrollTop = window.pageYOffset;
    
    if (scrollTop > 100) {
        header.classList.add('scrolled');
    } else {
        header.classList.remove('scrolled');
    }
    
    // Update reading progress (if on article page)
    updateReadingProgress();
}

/**
 * Update reading progress indicator
 */
function updateReadingProgress() {
    const article = document.querySelector('article');
    if (!article) return;
    
    const scrollTop = window.pageYOffset;
    const docHeight = document.body.scrollHeight - window.innerHeight;
    const scrollPercent = (scrollTop / docHeight) * 100;
    
    const progressBar = document.querySelector('.reading-progress');
    if (progressBar) {
        progressBar.style.width = scrollPercent + '%';
    }
}

/**
 * Preload critical resources
 */
function preloadCriticalResources() {
    // Preload critical CSS
    const criticalCSS = [
        '/static/css/rtl-arabic.css',
        '/static/css/responsive-mobile.css'
    ];
    
    criticalCSS.forEach(href => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.as = 'style';
        link.href = href;
        document.head.appendChild(link);
    });
}

/**
 * Initialize mobile accessibility features
 */
function initMobileAccessibility() {
    // Improve focus management
    const focusableElements = document.querySelectorAll(
        'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
    );
    
    focusableElements.forEach(element => {
        element.addEventListener('focus', function() {
            this.classList.add('focused');
        });
        
        element.addEventListener('blur', function() {
            this.classList.remove('focused');
        });
    });
    
    // Add skip links for mobile
    addSkipLinks();
    
    // Improve touch targets
    improveTouchTargets();
}

/**
 * Add skip navigation links
 */
function addSkipLinks() {
    const skipLinks = document.createElement('div');
    skipLinks.className = 'skip-links';
    skipLinks.innerHTML = `
        <a href="#main-content" class="skip-link">تخطي إلى المحتوى الرئيسي</a>
        <a href="#navigation" class="skip-link">تخطي إلى التنقل</a>
    `;
    
    document.body.insertBefore(skipLinks, document.body.firstChild);
}

/**
 * Improve touch targets for accessibility
 */
function improveTouchTargets() {
    const smallTargets = document.querySelectorAll('a, button');
    
    smallTargets.forEach(target => {
        const rect = target.getBoundingClientRect();
        if (rect.width < 44 || rect.height < 44) {
            target.classList.add('touch-target-improved');
        }
    });
}

/**
 * Handle orientation change
 */
function handleOrientationChange() {
    // Recalculate layout after orientation change
    setTimeout(function() {
        window.dispatchEvent(new Event('resize'));
        
        // Refresh any layout-dependent components
        refreshLayout();
    }, 100);
}

/**
 * Handle window resize
 */
function handleResize() {
    // Update mobile/desktop state
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        document.body.classList.add('mobile-view');
        document.body.classList.remove('desktop-view');
    } else {
        document.body.classList.add('desktop-view');
        document.body.classList.remove('mobile-view');
        
        // Close mobile menu if open
        const mobileMenu = document.querySelector('.mobile-menu');
        if (mobileMenu && !mobileMenu.classList.contains('hidden')) {
            mobileMenu.classList.add('hidden');
        }
    }
    
    refreshLayout();
}

/**
 * Refresh layout components
 */
function refreshLayout() {
    // Refresh masonry layouts if any
    const masonryContainers = document.querySelectorAll('.masonry');
    masonryContainers.forEach(container => {
        // Trigger masonry refresh
        container.dispatchEvent(new Event('refresh'));
    });
    
    // Update image sizes
    optimizeImagesForMobile();
}

/**
 * Initialize mobile-specific features
 */
function initMobileFeatures() {
    // Add pull-to-refresh (if supported)
    if ('serviceWorker' in navigator) {
        initPullToRefresh();
    }
    
    // Add mobile-specific gestures
    initMobileGestures();
    
    // Optimize form inputs for mobile
    optimizeFormsForMobile();
}

/**
 * Initialize pull-to-refresh
 */
function initPullToRefresh() {
    let startY = 0;
    let currentY = 0;
    let pulling = false;
    
    document.addEventListener('touchstart', function(e) {
        startY = e.touches[0].clientY;
    });
    
    document.addEventListener('touchmove', function(e) {
        currentY = e.touches[0].clientY;
        
        if (window.scrollY === 0 && currentY > startY + 50) {
            pulling = true;
            // Show pull-to-refresh indicator
        }
    });
    
    document.addEventListener('touchend', function() {
        if (pulling) {
            // Trigger refresh
            window.location.reload();
        }
        pulling = false;
    });
}

/**
 * Initialize mobile gestures
 */
function initMobileGestures() {
    // Double-tap to scroll to top
    let lastTap = 0;
    
    document.addEventListener('touchend', function(e) {
        const currentTime = new Date().getTime();
        const tapLength = currentTime - lastTap;
        
        if (tapLength < 500 && tapLength > 0) {
            // Double tap detected
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
        
        lastTap = currentTime;
    });
}

/**
 * Optimize forms for mobile
 */
function optimizeFormsForMobile() {
    const inputs = document.querySelectorAll('input, textarea');
    
    inputs.forEach(input => {
        // Add appropriate input types for mobile keyboards
        if (input.name === 'email') {
            input.type = 'email';
        } else if (input.name === 'phone') {
            input.type = 'tel';
        } else if (input.name === 'url') {
            input.type = 'url';
        }
        
        // Add autocomplete attributes
        if (input.name === 'name') {
            input.setAttribute('autocomplete', 'name');
        } else if (input.name === 'email') {
            input.setAttribute('autocomplete', 'email');
        }
    });
}

// Export functions for use in other scripts
window.MobileOptimization = {
    initMobileOptimization,
    handleOrientationChange,
    handleResize,
    refreshLayout
};
