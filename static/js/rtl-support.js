/**
 * RTL Support JavaScript
 * Enhanced functionality for Arabic RTL blog
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize RTL support
    initRTLSupport();
    
    // Initialize Arabic text enhancements
    initArabicTextEnhancements();
    
    // Initialize responsive RTL adjustments
    initResponsiveRTL();
    
    // Initialize keyboard navigation for RTL
    initRTLKeyboardNavigation();
});

/**
 * Initialize RTL support
 */
function initRTLSupport() {
    // Ensure HTML direction is set
    document.documentElement.setAttribute('dir', 'rtl');
    document.documentElement.setAttribute('lang', 'ar');
    
    // Add RTL class to body
    document.body.classList.add('rtl-layout');
    
    // Fix Tailwind space utilities for RTL
    fixTailwindSpacing();
    
    // Initialize RTL-aware animations
    initRTLAnimations();
}

/**
 * Fix Tailwind CSS spacing for RTL
 */
function fixTailwindSpacing() {
    // Convert space-x utilities to work with RTL
    const spaceXElements = document.querySelectorAll('[class*="space-x-"]');
    spaceXElements.forEach(element => {
        const classes = element.className.split(' ');
        classes.forEach(className => {
            if (className.includes('space-x-')) {
                element.classList.add('space-x-reverse');
            }
        });
    });
    
    // Handle margin and padding utilities
    const marginElements = document.querySelectorAll('[class*="ml-"], [class*="mr-"], [class*="pl-"], [class*="pr-"]');
    marginElements.forEach(element => {
        const classes = element.className.split(' ');
        let newClasses = [];
        
        classes.forEach(className => {
            if (className.startsWith('ml-')) {
                newClasses.push(className.replace('ml-', 'mr-'));
            } else if (className.startsWith('mr-')) {
                newClasses.push(className.replace('mr-', 'ml-'));
            } else if (className.startsWith('pl-')) {
                newClasses.push(className.replace('pl-', 'pr-'));
            } else if (className.startsWith('pr-')) {
                newClasses.push(className.replace('pr-', 'pl-'));
            } else {
                newClasses.push(className);
            }
        });
        
        element.className = newClasses.join(' ');
    });
}

/**
 * Initialize Arabic text enhancements
 */
function initArabicTextEnhancements() {
    // Add Arabic text class to all text elements
    const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, div, li, td, th');
    textElements.forEach(element => {
        if (isArabicText(element.textContent)) {
            element.classList.add('arabic-text');
        }
    });
    
    // Enhance Arabic numbers display
    enhanceArabicNumbers();
    
    // Fix Arabic punctuation
    fixArabicPunctuation();
}

/**
 * Check if text contains Arabic characters
 */
function isArabicText(text) {
    const arabicRegex = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/;
    return arabicRegex.test(text);
}

/**
 * Enhance Arabic numbers display
 */
function enhanceArabicNumbers() {
    const numberElements = document.querySelectorAll('.arabic-numbers');
    numberElements.forEach(element => {
        let text = element.textContent;
        // Convert Western Arabic numerals to Eastern Arabic numerals if needed
        const westernToEastern = {
            '0': '٠', '1': '١', '2': '٢', '3': '٣', '4': '٤',
            '5': '٥', '6': '٦', '7': '٧', '8': '٨', '9': '٩'
        };
        
        // Uncomment if you want to convert to Eastern Arabic numerals
        // text = text.replace(/[0-9]/g, function(match) {
        //     return westernToEastern[match];
        // });
        
        element.textContent = text;
    });
}

/**
 * Fix Arabic punctuation spacing
 */
function fixArabicPunctuation() {
    const textElements = document.querySelectorAll('.arabic-text');
    textElements.forEach(element => {
        let text = element.innerHTML;
        
        // Fix spacing around Arabic punctuation
        text = text.replace(/\s*،\s*/g, '، '); // Arabic comma
        text = text.replace(/\s*؛\s*/g, '؛ '); // Arabic semicolon
        text = text.replace(/\s*؟\s*/g, '؟ '); // Arabic question mark
        text = text.replace(/\s*!\s*/g, '! '); // Exclamation mark
        
        element.innerHTML = text;
    });
}

/**
 * Initialize responsive RTL adjustments
 */
function initResponsiveRTL() {
    // Handle responsive navigation
    const mobileMenuButton = document.querySelector('.mobile-menu-button');
    const mobileMenu = document.querySelector('.mobile-menu');
    
    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
            
            // Add RTL animation
            if (!mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.add('slide-in-rtl');
            }
        });
    }
    
    // Handle responsive tables
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        const wrapper = document.createElement('div');
        wrapper.className = 'table-responsive-rtl overflow-x-auto';
        table.parentNode.insertBefore(wrapper, table);
        wrapper.appendChild(table);
    });
}

/**
 * Initialize RTL-aware animations
 */
function initRTLAnimations() {
    // Intersection Observer for scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-fade-in-rtl');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    const animateElements = document.querySelectorAll('.article-card, .fade-in-rtl');
    animateElements.forEach(element => {
        observer.observe(element);
    });
}

/**
 * Initialize RTL keyboard navigation
 */
function initRTLKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // Handle RTL arrow key navigation
        if (e.key === 'ArrowRight') {
            // In RTL, right arrow should go to previous
            const activeElement = document.activeElement;
            const focusableElements = getFocusableElements();
            const currentIndex = Array.from(focusableElements).indexOf(activeElement);
            
            if (currentIndex > 0) {
                focusableElements[currentIndex - 1].focus();
                e.preventDefault();
            }
        } else if (e.key === 'ArrowLeft') {
            // In RTL, left arrow should go to next
            const activeElement = document.activeElement;
            const focusableElements = getFocusableElements();
            const currentIndex = Array.from(focusableElements).indexOf(activeElement);
            
            if (currentIndex < focusableElements.length - 1) {
                focusableElements[currentIndex + 1].focus();
                e.preventDefault();
            }
        }
    });
}

/**
 * Get all focusable elements
 */
function getFocusableElements() {
    return document.querySelectorAll(
        'a[href], button:not([disabled]), textarea:not([disabled]), ' +
        'input[type="text"]:not([disabled]), input[type="radio"]:not([disabled]), ' +
        'input[type="checkbox"]:not([disabled]), select:not([disabled])'
    );
}

/**
 * Utility function to add RTL-aware smooth scrolling
 */
function smoothScrollRTL(target, duration = 800) {
    const targetElement = document.querySelector(target);
    if (!targetElement) return;
    
    const targetPosition = targetElement.offsetTop;
    const startPosition = window.pageYOffset;
    const distance = targetPosition - startPosition;
    let startTime = null;
    
    function animation(currentTime) {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);
        if (timeElapsed < duration) requestAnimationFrame(animation);
    }
    
    function ease(t, b, c, d) {
        t /= d / 2;
        if (t < 1) return c / 2 * t * t + b;
        t--;
        return -c / 2 * (t * (t - 2) - 1) + b;
    }
    
    requestAnimationFrame(animation);
}

/**
 * Handle form validation messages in Arabic
 */
function initArabicFormValidation() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const inputs = form.querySelectorAll('input[required], textarea[required]');
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.setCustomValidity('هذا الحقل مطلوب');
                } else {
                    input.setCustomValidity('');
                }
            });
        });
    });
}

/**
 * Initialize Arabic date formatting
 */
function initArabicDateFormatting() {
    const dateElements = document.querySelectorAll('.arabic-date');
    dateElements.forEach(element => {
        const date = new Date(element.textContent);
        const options = {
            year: 'numeric',
            month: 'long',
            day: 'numeric',
            calendar: 'islamic'
        };
        
        try {
            const arabicDate = date.toLocaleDateString('ar-SA', options);
            element.textContent = arabicDate;
        } catch (e) {
            // Fallback to regular Arabic date formatting
            const arabicMonths = [
                'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
                'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
            ];
            
            const day = date.getDate();
            const month = arabicMonths[date.getMonth()];
            const year = date.getFullYear();
            
            element.textContent = `${day} ${month} ${year}`;
        }
    });
}

/**
 * Add CSS animations for RTL
 */
function addRTLAnimationStyles() {
    const style = document.createElement('style');
    style.textContent = `
        .animate-fade-in-rtl {
            animation: fadeInRTL 0.6s ease-out forwards;
        }
        
        @keyframes fadeInRTL {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .table-responsive-rtl {
            direction: rtl;
        }
        
        .table-responsive-rtl table {
            min-width: 100%;
        }
    `;
    document.head.appendChild(style);
}

// Initialize additional RTL features
document.addEventListener('DOMContentLoaded', function() {
    initArabicFormValidation();
    initArabicDateFormatting();
    addRTLAnimationStyles();
});

// Export functions for use in other scripts
window.RTLSupport = {
    smoothScrollRTL,
    isArabicText,
    initRTLSupport,
    initArabicTextEnhancements
};
