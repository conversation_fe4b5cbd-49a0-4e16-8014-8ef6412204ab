<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ page_title|default:"المدونة العربية" }}{% endblock %}</title>

    <!-- SEO Meta Tags -->
    <meta name="description" content="{% block description %}مدونة عربية متخصصة في المحتوى العربي الأصيل{% endblock %}">
    <meta name="keywords" content="{% block keywords %}مدونة, عربية, محتوى, مقالات{% endblock %}">
    <meta name="author" content="المدونة العربية">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="{% block og_title %}{{ page_title|default:"المدونة العربية" }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}مدونة عربية متخصصة في المحتوى العربي الأصيل{% endblock %}">
    <meta property="og:type" content="website">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Google Fonts - IBM Plex Sans Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Custom RTL CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/rtl-arabic.css' %}">
    <link rel="stylesheet" href="{% static 'css/responsive-mobile.css' %}">

    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'arabic': ['IBM Plex Sans Arabic', 'sans-serif'],
                    },
                    colors: {
                        'primary': '#00BFA6',
                        'secondary': '#0F0F0F',
                        'accent': '#FF6B35',
                        'gold': '#FFD700',
                    }
                }
            }
        }
    </script>

    <!-- Custom CSS -->
    <style>
        body {
            font-family: 'IBM Plex Sans Arabic', sans-serif;
        }

        /* RTL specific styles */
        .rtl-flip {
            transform: scaleX(-1);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #00BFA6;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #00A693;
        }

        /* Smooth transitions */
        * {
            transition: all 0.3s ease;
        }

        /* Custom button hover effects */
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0, 191, 166, 0.3);
        }

        /* Article card hover effect */
        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50 text-gray-900 font-arabic">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{% url 'blog:home' %}" class="text-2xl font-bold text-primary">
                        المدونة العربية
                    </a>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="mr-10 flex items-baseline space-x-4 space-x-reverse">
                        <a href="{% url 'blog:home' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            الرئيسية
                        </a>
                        <a href="{% url 'blog:about' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            حول المدونة
                        </a>
                        <a href="{% url 'blog:contact' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            اتصل بنا
                        </a>
                    </div>
                </div>

                <!-- Search -->
                <div class="hidden md:block">
                    <form method="GET" action="{% url 'blog:search' %}" class="flex">
                        <input type="text" name="q" placeholder="البحث..."
                               class="px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               value="{{ request.GET.q }}">
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-l-md hover:bg-opacity-90 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-button text-gray-700 hover:text-primary focus:outline-none focus:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Navigation -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="{% url 'blog:home' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    الرئيسية
                </a>
                <a href="{% url 'blog:about' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    حول المدونة
                </a>
                <a href="{% url 'blog:contact' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    اتصل بنا
                </a>

                <!-- Mobile Search -->
                <div class="px-3 py-2">
                    <form method="GET" action="{% url 'blog:search' %}" class="flex">
                        <input type="text" name="q" placeholder="البحث..."
                               class="flex-1 px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary"
                               value="{{ request.GET.q }}">
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-l-md hover:bg-opacity-90">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="min-h-screen">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-secondary text-white py-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- About -->
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary">المدونة العربية</h3>
                    <p class="text-gray-300 leading-relaxed">
                        مدونة عربية متخصصة في تقديم محتوى عربي أصيل وهادف يثري المحتوى العربي على الإنترنت.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="{% url 'blog:home' %}" class="text-gray-300 hover:text-primary transition-colors">الرئيسية</a></li>
                        <li><a href="{% url 'blog:about' %}" class="text-gray-300 hover:text-primary transition-colors">حول المدونة</a></li>
                        <li><a href="{% url 'blog:contact' %}" class="text-gray-300 hover:text-primary transition-colors">اتصل بنا</a></li>
                        <li><a href="/admin/" class="text-gray-300 hover:text-primary transition-colors">لوحة التحكم</a></li>
                    </ul>
                </div>

                <!-- Social Media -->
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary">تابعنا</h3>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>

            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">
                    &copy; {% now "Y" %} المدونة العربية. جميع الحقوق محفوظة.
                </p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.querySelector('.mobile-menu-button').addEventListener('click', function() {
            document.querySelector('.mobile-menu').classList.toggle('hidden');
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>

    <!-- RTL Support JavaScript -->
    <script src="{% static 'js/rtl-support.js' %}"></script>
    <script src="{% static 'js/mobile-optimization.js' %}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
<body class="bg-gray-50 text-gray-900 font-arabic">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="{% url 'blog:home' %}" class="text-2xl font-bold text-primary">
                        المدونة العربية
                    </a>
                </div>
                
                <!-- Desktop Navigation -->
                <div class="hidden md:block">
                    <div class="mr-10 flex items-baseline space-x-4 space-x-reverse">
                        <a href="{% url 'blog:home' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            الرئيسية
                        </a>
                        <a href="{% url 'blog:about' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            حول المدونة
                        </a>
                        <a href="{% url 'blog:contact' %}" class="text-gray-700 hover:text-primary px-3 py-2 rounded-md text-sm font-medium transition-colors">
                            اتصل بنا
                        </a>
                    </div>
                </div>
                
                <!-- Search -->
                <div class="hidden md:block">
                    <form method="GET" action="{% url 'blog:search' %}" class="flex">
                        <input type="text" name="q" placeholder="البحث..." 
                               class="px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                               value="{{ request.GET.q }}">
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-l-md hover:bg-opacity-90 transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
                
                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button" class="mobile-menu-button text-gray-700 hover:text-primary focus:outline-none focus:text-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Navigation -->
        <div class="mobile-menu hidden md:hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t">
                <a href="{% url 'blog:home' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    الرئيسية
                </a>
                <a href="{% url 'blog:about' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    حول المدونة
                </a>
                <a href="{% url 'blog:contact' %}" class="text-gray-700 hover:text-primary block px-3 py-2 rounded-md text-base font-medium">
                    اتصل بنا
                </a>
                
                <!-- Mobile Search -->
                <div class="px-3 py-2">
                    <form method="GET" action="{% url 'blog:search' %}" class="flex">
                        <input type="text" name="q" placeholder="البحث..." 
                               class="flex-1 px-4 py-2 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-primary"
                               value="{{ request.GET.q }}">
                        <button type="submit" class="bg-primary text-white px-4 py-2 rounded-l-md hover:bg-opacity-90">
                            <i class="fas fa-search"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="min-h-screen">
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-secondary text-white py-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- About -->
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary">المدونة العربية</h3>
                    <p class="text-gray-300 leading-relaxed">
                        مدونة عربية متخصصة في تقديم محتوى عربي أصيل وهادف يثري المحتوى العربي على الإنترنت.
                    </p>
                </div>
                
                <!-- Quick Links -->
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary">روابط سريعة</h3>
                    <ul class="space-y-2">
                        <li><a href="{% url 'blog:home' %}" class="text-gray-300 hover:text-primary transition-colors">الرئيسية</a></li>
                        <li><a href="{% url 'blog:about' %}" class="text-gray-300 hover:text-primary transition-colors">حول المدونة</a></li>
                        <li><a href="{% url 'blog:contact' %}" class="text-gray-300 hover:text-primary transition-colors">اتصل بنا</a></li>
                        <li><a href="/admin/" class="text-gray-300 hover:text-primary transition-colors">لوحة التحكم</a></li>
                    </ul>
                </div>
                
                <!-- Social Media -->
                <div>
                    <h3 class="text-xl font-bold mb-4 text-primary">تابعنا</h3>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="text-gray-300 hover:text-primary text-2xl transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300">
                    &copy; {% now "Y" %} المدونة العربية. جميع الحقوق محفوظة.
                </p>
            </div>
        </div>
    </footer>
    
    <!-- JavaScript -->
    <script>
        // Mobile menu toggle
        document.querySelector('.mobile-menu-button').addEventListener('click', function() {
            document.querySelector('.mobile-menu').classList.toggle('hidden');
        });
        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
