{% extends 'base.html' %}

{% block title %}الكلمة المفتاحية: {{ tag }} - المدونة العربية{% endblock %}

{% block content %}
<!-- Tag Header -->
<section class="bg-gradient-to-l from-primary to-blue-600 text-white py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-3xl md:text-4xl font-bold mb-4">
            <i class="fas fa-tag ml-3"></i>{{ tag }}
        </h1>
        <p class="text-xl text-gray-100">
            جميع المقالات المتعلقة بـ "{{ tag }}"
        </p>
        <div class="mt-6">
            <span class="bg-white bg-opacity-20 px-4 py-2 rounded-full text-sm">
                {{ total_results }} مقال
            </span>
        </div>
    </div>
</section>

<!-- Articles -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if articles %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for article in articles %}
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden article-card">
                        {% if article.image %}
                            <img src="{{ article.image.url }}" alt="{{ article.title }}" 
                                 class="w-full h-48 object-cover">
                        {% else %}
                            <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <i class="fas fa-newspaper text-gray-500 text-4xl"></i>
                            </div>
                        {% endif %}
                        
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-primary transition-colors">
                                <a href="{{ article.get_absolute_url }}">{{ article.title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                {{ article.excerpt|truncatewords:15 }}
                            </p>
                            
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span><i class="fas fa-calendar-alt ml-2"></i>{{ article.published_at|date:"d F Y" }}</span>
                                <span><i class="fas fa-eye ml-2"></i>{{ article.views_count }} مشاهدة</span>
                            </div>
                            
                            <a href="{{ article.get_absolute_url }}" 
                               class="inline-block bg-primary text-white px-6 py-2 rounded-md hover:bg-opacity-90 transition-colors btn-hover">
                                اقرأ المزيد
                            </a>
                        </div>
                    </article>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if articles.has_other_pages %}
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-2 space-x-reverse">
                        {% if articles.has_previous %}
                            <a href="?page={{ articles.previous_page_number }}" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                السابق
                            </a>
                        {% endif %}
                        
                        {% for num in articles.paginator.page_range %}
                            {% if articles.number == num %}
                                <span class="px-4 py-2 bg-primary text-white rounded-md">{{ num }}</span>
                            {% elif num > articles.number|add:'-3' and num < articles.number|add:'3' %}
                                <a href="?page={{ num }}" 
                                   class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if articles.has_next %}
                            <a href="?page={{ articles.next_page_number }}" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                التالي
                            </a>
                        {% endif %}
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-16">
                <i class="fas fa-tag text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-600 mb-2">لا توجد مقالات</h3>
                <p class="text-gray-500">لم يتم العثور على مقالات بالكلمة المفتاحية "{{ tag }}"</p>
                <a href="{% url 'blog:home' %}" 
                   class="mt-6 inline-block bg-primary text-white px-6 py-3 rounded-lg hover:bg-opacity-90 transition-colors btn-hover">
                    العودة إلى الرئيسية
                </a>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}
