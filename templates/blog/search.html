{% extends 'base.html' %}

{% block title %}
    {% if query %}البحث: {{ query }} - المدونة العربية{% else %}البحث - المدونة العربية{% endif %}
{% endblock %}

{% block content %}
<!-- Search Header -->
<section class="bg-gradient-to-l from-primary to-blue-600 text-white py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-3xl md:text-4xl font-bold mb-6">البحث في المدونة</h1>
        <p class="text-xl text-gray-100 mb-8">ابحث عن المقالات والمحتوى الذي يهمك</p>
        
        <!-- Search Form -->
        <form method="GET" class="max-w-2xl mx-auto">
            <div class="flex">
                <input type="text" name="q" placeholder="ابحث عن المقالات..." 
                       value="{{ query }}"
                       class="flex-1 px-6 py-4 text-gray-900 rounded-r-lg focus:outline-none focus:ring-2 focus:ring-white text-lg">
                <button type="submit" 
                        class="bg-white text-primary px-8 py-4 rounded-l-lg hover:bg-gray-100 transition-colors font-bold">
                    <i class="fas fa-search ml-2"></i>بحث
                </button>
            </div>
        </form>
    </div>
</section>

<!-- Search Results -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {% if query %}
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-900 mb-2">
                    نتائج البحث عن: "{{ query }}"
                </h2>
                <p class="text-gray-600">
                    تم العثور على {{ total_results }} نتيجة
                </p>
            </div>
        {% endif %}
        
        {% if articles %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for article in articles %}
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden article-card">
                        {% if article.image %}
                            <img src="{{ article.image.url }}" alt="{{ article.title }}" 
                                 class="w-full h-48 object-cover">
                        {% else %}
                            <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <i class="fas fa-newspaper text-gray-500 text-4xl"></i>
                            </div>
                        {% endif %}
                        
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-primary transition-colors">
                                <a href="{{ article.get_absolute_url }}">{{ article.title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                {{ article.excerpt|truncatewords:15 }}
                            </p>
                            
                            <!-- Tags -->
                            {% if article.get_tags_list %}
                                <div class="mb-4">
                                    {% for tag in article.get_tags_list %}
                                        <a href="{% url 'blog:tag' tag %}" 
                                           class="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm ml-2 mb-2 hover:bg-primary hover:text-white transition-colors">
                                            {{ tag }}
                                        </a>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span><i class="fas fa-calendar-alt ml-2"></i>{{ article.published_at|date:"d F Y" }}</span>
                                <span><i class="fas fa-eye ml-2"></i>{{ article.views_count }} مشاهدة</span>
                            </div>
                            
                            <a href="{{ article.get_absolute_url }}" 
                               class="inline-block bg-primary text-white px-6 py-2 rounded-md hover:bg-opacity-90 transition-colors btn-hover">
                                اقرأ المزيد
                            </a>
                        </div>
                    </article>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if articles.has_other_pages %}
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-2 space-x-reverse">
                        {% if articles.has_previous %}
                            <a href="?q={{ query }}&page={{ articles.previous_page_number }}" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                السابق
                            </a>
                        {% endif %}
                        
                        {% for num in articles.paginator.page_range %}
                            {% if articles.number == num %}
                                <span class="px-4 py-2 bg-primary text-white rounded-md">{{ num }}</span>
                            {% elif num > articles.number|add:'-3' and num < articles.number|add:'3' %}
                                <a href="?q={{ query }}&page={{ num }}" 
                                   class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if articles.has_next %}
                            <a href="?q={{ query }}&page={{ articles.next_page_number }}" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                التالي
                            </a>
                        {% endif %}
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-16">
                {% if query %}
                    <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-2xl font-bold text-gray-600 mb-2">لم يتم العثور على نتائج</h3>
                    <p class="text-gray-500 mb-8">لم نجد أي مقالات تحتوي على "{{ query }}"</p>
                    <div class="max-w-md mx-auto">
                        <h4 class="text-lg font-semibold text-gray-700 mb-4">اقتراحات للبحث:</h4>
                        <ul class="text-gray-600 space-y-2">
                            <li>• تأكد من صحة الكلمات المكتوبة</li>
                            <li>• جرب كلمات مفتاحية أخرى</li>
                            <li>• استخدم كلمات أكثر عمومية</li>
                            <li>• تأكد من عدم وجود أخطاء إملائية</li>
                        </ul>
                    </div>
                {% else %}
                    <i class="fas fa-search text-gray-400 text-6xl mb-4"></i>
                    <h3 class="text-2xl font-bold text-gray-600 mb-2">ابدأ البحث</h3>
                    <p class="text-gray-500">استخدم مربع البحث أعلاه للعثور على المقالات</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</section>

<!-- Popular Tags -->
<section class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">الكلمات المفتاحية الشائعة</h2>
        <div class="flex flex-wrap justify-center gap-3">
            <!-- You can populate these from your database -->
            <a href="{% url 'blog:tag' 'تقنية' %}" 
               class="bg-white text-gray-700 px-4 py-2 rounded-full hover:bg-primary hover:text-white transition-colors shadow-sm">
                تقنية
            </a>
            <a href="{% url 'blog:tag' 'برمجة' %}" 
               class="bg-white text-gray-700 px-4 py-2 rounded-full hover:bg-primary hover:text-white transition-colors shadow-sm">
                برمجة
            </a>
            <a href="{% url 'blog:tag' 'تطوير' %}" 
               class="bg-white text-gray-700 px-4 py-2 rounded-full hover:bg-primary hover:text-white transition-colors shadow-sm">
                تطوير
            </a>
            <a href="{% url 'blog:tag' 'تصميم' %}" 
               class="bg-white text-gray-700 px-4 py-2 rounded-full hover:bg-primary hover:text-white transition-colors shadow-sm">
                تصميم
            </a>
            <a href="{% url 'blog:tag' 'ذكي' %}" 
               class="bg-white text-gray-700 px-4 py-2 rounded-full hover:bg-primary hover:text-white transition-colors shadow-sm">
                ذكاء اصطناعي
            </a>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-focus search input
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.querySelector('input[name="q"]');
        if (searchInput && !searchInput.value) {
            searchInput.focus();
        }
    });
    
    // Search suggestions (you can enhance this with AJAX)
    document.querySelector('input[name="q"]').addEventListener('input', function() {
        // Add search suggestions functionality here
    });
</script>
{% endblock %}
