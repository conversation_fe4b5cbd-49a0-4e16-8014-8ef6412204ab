{% extends 'base.html' %}

{% block title %}اتصل بنا - المدونة العربية{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="bg-gradient-to-l from-primary to-blue-600 text-white py-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-bold mb-6">اتصل بنا</h1>
        <p class="text-xl text-gray-100 leading-relaxed">
            نحن هنا للاستماع إليك والإجابة على استفساراتك
        </p>
    </div>
</section>

<!-- Contact Form -->
<section class="py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Contact Form -->
            <div>
                <h2 class="text-2xl font-bold text-gray-900 mb-6">أرسل لنا رسالة</h2>
                <form id="contactForm" class="space-y-6">
                    {% csrf_token %}
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                        <input type="text" id="name" name="name" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                        <input type="email" id="email" name="email" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">الموضوع</label>
                        <input type="text" id="subject" name="subject" required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    </div>
                    
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">الرسالة</label>
                        <textarea id="message" name="message" rows="6" required
                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"></textarea>
                    </div>
                    
                    <button type="submit" 
                            class="w-full bg-primary text-white py-3 px-6 rounded-lg hover:bg-opacity-90 transition-colors btn-hover font-medium">
                        <i class="fas fa-paper-plane ml-2"></i>إرسال الرسالة
                    </button>
                </form>
                
                <!-- Success/Error Messages -->
                <div id="messageAlert" class="hidden mt-4 p-4 rounded-lg"></div>
            </div>
            
            <!-- Contact Info -->
            <div>
                <h2 class="text-2xl font-bold text-gray-900 mb-6">معلومات الاتصال</h2>
                
                <div class="space-y-6">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-primary text-white rounded-lg flex items-center justify-center">
                                <i class="fas fa-envelope"></i>
                            </div>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-medium text-gray-900">البريد الإلكتروني</h3>
                            <p class="text-gray-600"><EMAIL></p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-primary text-white rounded-lg flex items-center justify-center">
                                <i class="fas fa-phone"></i>
                            </div>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-medium text-gray-900">الهاتف</h3>
                            <p class="text-gray-600">+966 50 123 4567</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-primary text-white rounded-lg flex items-center justify-center">
                                <i class="fas fa-clock"></i>
                            </div>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-medium text-gray-900">ساعات العمل</h3>
                            <p class="text-gray-600">الأحد - الخميس: 9:00 ص - 6:00 م</p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <div class="w-10 h-10 bg-primary text-white rounded-lg flex items-center justify-center">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                        </div>
                        <div class="mr-4">
                            <h3 class="text-lg font-medium text-gray-900">الموقع</h3>
                            <p class="text-gray-600">الرياض، المملكة العربية السعودية</p>
                        </div>
                    </div>
                </div>
                
                <!-- Social Media -->
                <div class="mt-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">تابعنا على</h3>
                    <div class="flex space-x-4 space-x-reverse">
                        <a href="#" class="w-10 h-10 bg-blue-600 text-white rounded-lg flex items-center justify-center hover:bg-blue-700 transition-colors">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-400 text-white rounded-lg flex items-center justify-center hover:bg-blue-500 transition-colors">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-pink-600 text-white rounded-lg flex items-center justify-center hover:bg-pink-700 transition-colors">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-blue-700 text-white rounded-lg flex items-center justify-center hover:bg-blue-800 transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="bg-gray-50 py-16">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 text-center mb-12">الأسئلة الشائعة</h2>
        
        <div class="space-y-6">
            <div class="bg-white rounded-lg shadow-sm">
                <button class="faq-button w-full text-right p-6 focus:outline-none" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-medium text-gray-900">كيف يمكنني المساهمة في المدونة؟</span>
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform"></i>
                    </div>
                </button>
                <div class="faq-content hidden px-6 pb-6">
                    <p class="text-gray-600">
                        يمكنك المساهمة في المدونة من خلال إرسال مقالاتك أو أفكارك عبر نموذج الاتصال. 
                        سيقوم فريقنا بمراجعة المحتوى والتواصل معك.
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm">
                <button class="faq-button w-full text-right p-6 focus:outline-none" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-medium text-gray-900">هل يمكنني طلب موضوع معين؟</span>
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform"></i>
                    </div>
                </button>
                <div class="faq-content hidden px-6 pb-6">
                    <p class="text-gray-600">
                        بالطبع! نحن نرحب بطلبات المواضيع من قرائنا. أرسل لنا اقتراحك وسنعمل على تغطيته.
                    </p>
                </div>
            </div>
            
            <div class="bg-white rounded-lg shadow-sm">
                <button class="faq-button w-full text-right p-6 focus:outline-none" onclick="toggleFAQ(this)">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-medium text-gray-900">كم مدة الرد على الرسائل؟</span>
                        <i class="fas fa-chevron-down text-gray-500 transform transition-transform"></i>
                    </div>
                </button>
                <div class="faq-content hidden px-6 pb-6">
                    <p class="text-gray-600">
                        نحرص على الرد على جميع الرسائل خلال 24-48 ساعة من استلامها.
                    </p>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Contact form submission
    document.getElementById('contactForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(this);
        const messageAlert = document.getElementById('messageAlert');
        
        fetch('{% url "blog:contact" %}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                messageAlert.className = 'mt-4 p-4 rounded-lg bg-green-100 text-green-700 border border-green-200';
                messageAlert.textContent = data.message;
                this.reset();
            } else {
                messageAlert.className = 'mt-4 p-4 rounded-lg bg-red-100 text-red-700 border border-red-200';
                messageAlert.textContent = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.';
            }
            messageAlert.classList.remove('hidden');
            
            // Hide message after 5 seconds
            setTimeout(() => {
                messageAlert.classList.add('hidden');
            }, 5000);
        })
        .catch(error => {
            messageAlert.className = 'mt-4 p-4 rounded-lg bg-red-100 text-red-700 border border-red-200';
            messageAlert.textContent = 'حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى.';
            messageAlert.classList.remove('hidden');
        });
    });
    
    // FAQ toggle function
    function toggleFAQ(button) {
        const content = button.nextElementSibling;
        const icon = button.querySelector('i');
        
        if (content.classList.contains('hidden')) {
            content.classList.remove('hidden');
            icon.classList.add('rotate-180');
        } else {
            content.classList.add('hidden');
            icon.classList.remove('rotate-180');
        }
    }
</script>
{% endblock %}
