{% extends 'base.html' %}

{% block title %}الرئيسية - المدونة العربية{% endblock %}

{% block content %}
<!-- Hero Section -->
{% if featured_article %}
<section class="bg-gradient-to-l from-primary to-blue-600 text-white py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl md:text-6xl font-bold mb-6 leading-tight">
                    مرحباً بك في المدونة العربية
                </h1>
                <p class="text-xl mb-8 text-gray-100 leading-relaxed">
                    اكتشف محتوى عربي أصيل وهادف يثري معرفتك ويوسع آفاقك
                </p>
                <a href="#articles" class="bg-white text-primary px-8 py-4 rounded-lg font-bold text-lg btn-hover inline-block">
                    استكشف المقالات
                </a>
            </div>
            
            <div class="relative">
                <div class="bg-white rounded-lg shadow-2xl overflow-hidden article-card">
                    {% if featured_article.image %}
                        <img src="{{ featured_article.image.url }}" alt="{{ featured_article.title }}" 
                             class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-gradient-to-br from-primary to-blue-500 flex items-center justify-center">
                            <i class="fas fa-newspaper text-white text-6xl"></i>
                        </div>
                    {% endif %}
                    
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-gray-900 mb-3">{{ featured_article.title }}</h3>
                        <p class="text-gray-600 mb-4 leading-relaxed">{{ featured_article.excerpt|truncatewords:20 }}</p>
                        <div class="flex items-center justify-between text-sm text-gray-500">
                            <span><i class="fas fa-calendar-alt ml-2"></i>{{ featured_article.published_at|date:"d F Y" }}</span>
                            <span><i class="fas fa-eye ml-2"></i>{{ featured_article.views_count }} مشاهدة</span>
                        </div>
                        <a href="{{ featured_article.get_absolute_url }}" 
                           class="mt-4 inline-block bg-primary text-white px-6 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                            اقرأ المزيد
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endif %}

<!-- Articles Section -->
<section id="articles" class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">أحدث المقالات</h2>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                اكتشف مجموعة متنوعة من المقالات المفيدة والمثيرة للاهتمام
            </p>
        </div>
        
        {% if articles %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for article in articles %}
                    <article class="bg-white rounded-lg shadow-lg overflow-hidden article-card">
                        {% if article.image %}
                            <img src="{{ article.image.url }}" alt="{{ article.title }}" 
                                 class="w-full h-48 object-cover">
                        {% else %}
                            <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                                <i class="fas fa-newspaper text-gray-500 text-4xl"></i>
                            </div>
                        {% endif %}
                        
                        <div class="p-6">
                            <h3 class="text-xl font-bold text-gray-900 mb-3 hover:text-primary transition-colors">
                                <a href="{{ article.get_absolute_url }}">{{ article.title }}</a>
                            </h3>
                            
                            <p class="text-gray-600 mb-4 leading-relaxed">
                                {{ article.excerpt|truncatewords:15 }}
                            </p>
                            
                            <!-- Tags -->
                            {% if article.get_tags_list %}
                                <div class="mb-4">
                                    {% for tag in article.get_tags_list %}
                                        <a href="{% url 'blog:tag' tag %}" 
                                           class="inline-block bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm ml-2 mb-2 hover:bg-primary hover:text-white transition-colors">
                                            {{ tag }}
                                        </a>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            
                            <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                                <span><i class="fas fa-calendar-alt ml-2"></i>{{ article.published_at|date:"d F Y" }}</span>
                                <span><i class="fas fa-eye ml-2"></i>{{ article.views_count }} مشاهدة</span>
                            </div>
                            
                            <a href="{{ article.get_absolute_url }}" 
                               class="inline-block bg-primary text-white px-6 py-2 rounded-md hover:bg-opacity-90 transition-colors btn-hover">
                                اقرأ المزيد
                            </a>
                        </div>
                    </article>
                {% endfor %}
            </div>
            
            <!-- Pagination -->
            {% if articles.has_other_pages %}
                <div class="mt-12 flex justify-center">
                    <nav class="flex items-center space-x-2 space-x-reverse">
                        {% if articles.has_previous %}
                            <a href="?page={{ articles.previous_page_number }}" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                السابق
                            </a>
                        {% endif %}
                        
                        {% for num in articles.paginator.page_range %}
                            {% if articles.number == num %}
                                <span class="px-4 py-2 bg-primary text-white rounded-md">{{ num }}</span>
                            {% elif num > articles.number|add:'-3' and num < articles.number|add:'3' %}
                                <a href="?page={{ num }}" 
                                   class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                    {{ num }}
                                </a>
                            {% endif %}
                        {% endfor %}
                        
                        {% if articles.has_next %}
                            <a href="?page={{ articles.next_page_number }}" 
                               class="px-4 py-2 bg-white border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
                                التالي
                            </a>
                        {% endif %}
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="text-center py-16">
                <i class="fas fa-newspaper text-gray-400 text-6xl mb-4"></i>
                <h3 class="text-2xl font-bold text-gray-600 mb-2">لا توجد مقالات حالياً</h3>
                <p class="text-gray-500">سيتم نشر المقالات قريباً</p>
            </div>
        {% endif %}
    </div>
</section>

<!-- Call to Action -->
<section class="bg-gray-100 py-16">
    <div class="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold text-gray-900 mb-4">هل تريد البقاء على اطلاع؟</h2>
        <p class="text-xl text-gray-600 mb-8">
            تابع أحدث المقالات والمحتوى الحصري من خلال وسائل التواصل الاجتماعي
        </p>
        <div class="flex justify-center space-x-4 space-x-reverse">
            <a href="#" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors btn-hover">
                <i class="fab fa-facebook ml-2"></i>فيسبوك
            </a>
            <a href="#" class="bg-blue-400 text-white px-6 py-3 rounded-lg hover:bg-blue-500 transition-colors btn-hover">
                <i class="fab fa-twitter ml-2"></i>تويتر
            </a>
            <a href="#" class="bg-pink-600 text-white px-6 py-3 rounded-lg hover:bg-pink-700 transition-colors btn-hover">
                <i class="fab fa-instagram ml-2"></i>إنستغرام
            </a>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    // Smooth scroll to articles section
    document.addEventListener('DOMContentLoaded', function() {
        const exploreBtn = document.querySelector('a[href="#articles"]');
        if (exploreBtn) {
            exploreBtn.addEventListener('click', function(e) {
                e.preventDefault();
                document.querySelector('#articles').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        }
    });
</script>
{% endblock %}
