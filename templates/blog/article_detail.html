{% extends 'base.html' %}

{% block title %}{{ article.title }} - المدونة العربية{% endblock %}

{% block description %}{{ article.excerpt|default:article.content|truncatewords:20 }}{% endblock %}

{% block keywords %}{{ article.tags }}, مدونة, عربية, مقال{% endblock %}

{% block og_title %}{{ article.title }}{% endblock %}
{% block og_description %}{{ article.excerpt|default:article.content|truncatewords:20 }}{% endblock %}

{% block content %}
<!-- Breadcrumb -->
<nav class="bg-gray-100 py-4">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <ol class="flex items-center space-x-2 space-x-reverse text-sm text-gray-600">
            <li><a href="{% url 'blog:home' %}" class="hover:text-primary transition-colors">الرئيسية</a></li>
            <li><i class="fas fa-chevron-left mx-2"></i></li>
            <li class="text-gray-900">{{ article.title|truncatewords:5 }}</li>
        </ol>
    </div>
</nav>

<!-- Article Content -->
<article class="py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Article Header -->
        <header class="mb-8">
            <h1 class="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                {{ article.title }}
            </h1>
            
            <!-- Article Meta -->
            <div class="flex flex-wrap items-center text-gray-600 text-sm mb-6 space-y-2 md:space-y-0">
                <div class="flex items-center ml-6">
                    <i class="fas fa-calendar-alt ml-2 text-primary"></i>
                    <span>{{ article.published_at|date:"d F Y" }}</span>
                </div>
                <div class="flex items-center ml-6">
                    <i class="fas fa-user ml-2 text-primary"></i>
                    <span>{{ article.author.get_full_name|default:article.author.username }}</span>
                </div>
                <div class="flex items-center ml-6">
                    <i class="fas fa-eye ml-2 text-primary"></i>
                    <span>{{ article.views_count }} مشاهدة</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-clock ml-2 text-primary"></i>
                    <span>{{ article.content|wordcount|floatformat:0|add:"60"|div:200 }} دقائق قراءة</span>
                </div>
            </div>
            
            <!-- Tags -->
            {% if article.get_tags_list %}
                <div class="mb-6">
                    <div class="flex flex-wrap gap-2">
                        {% for tag in article.get_tags_list %}
                            <a href="{% url 'blog:tag' tag %}" 
                               class="inline-block bg-primary bg-opacity-10 text-primary px-3 py-1 rounded-full text-sm hover:bg-primary hover:text-white transition-colors">
                                <i class="fas fa-tag ml-1"></i>{{ tag }}
                            </a>
                        {% endfor %}
                    </div>
                </div>
            {% endif %}
        </header>
        
        <!-- Featured Image -->
        {% if article.image %}
            <div class="mb-8">
                <img src="{{ article.image.url }}" alt="{{ article.title }}" 
                     class="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg">
            </div>
        {% endif %}
        
        <!-- Article Content -->
        <div class="prose prose-lg max-w-none text-gray-800 leading-relaxed">
            {{ article.content|linebreaks }}
        </div>
        
        <!-- Share Buttons -->
        <div class="mt-12 pt-8 border-t border-gray-200">
            <h3 class="text-lg font-bold text-gray-900 mb-4">شارك المقال</h3>
            <div class="flex space-x-4 space-x-reverse">
                <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}" 
                   target="_blank" 
                   class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors btn-hover">
                    <i class="fab fa-facebook ml-2"></i>فيسبوك
                </a>
                <a href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text={{ article.title }}" 
                   target="_blank" 
                   class="bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors btn-hover">
                    <i class="fab fa-twitter ml-2"></i>تويتر
                </a>
                <a href="https://wa.me/?text={{ article.title }} {{ request.build_absolute_uri }}" 
                   target="_blank" 
                   class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors btn-hover">
                    <i class="fab fa-whatsapp ml-2"></i>واتساب
                </a>
                <button onclick="copyToClipboard()" 
                        class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors btn-hover">
                    <i class="fas fa-link ml-2"></i>نسخ الرابط
                </button>
            </div>
        </div>
    </div>
</article>

<!-- Related Articles -->
{% if related_articles %}
<section class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 class="text-2xl md:text-3xl font-bold text-gray-900 mb-8 text-center">مقالات ذات صلة</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            {% for related_article in related_articles %}
                <article class="bg-white rounded-lg shadow-lg overflow-hidden article-card">
                    {% if related_article.image %}
                        <img src="{{ related_article.image.url }}" alt="{{ related_article.title }}" 
                             class="w-full h-48 object-cover">
                    {% else %}
                        <div class="w-full h-48 bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center">
                            <i class="fas fa-newspaper text-gray-500 text-4xl"></i>
                        </div>
                    {% endif %}
                    
                    <div class="p-6">
                        <h3 class="text-lg font-bold text-gray-900 mb-3 hover:text-primary transition-colors">
                            <a href="{{ related_article.get_absolute_url }}">{{ related_article.title }}</a>
                        </h3>
                        
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            {{ related_article.excerpt|truncatewords:10 }}
                        </p>
                        
                        <div class="flex items-center justify-between text-sm text-gray-500 mb-4">
                            <span><i class="fas fa-calendar-alt ml-2"></i>{{ related_article.published_at|date:"d F Y" }}</span>
                            <span><i class="fas fa-eye ml-2"></i>{{ related_article.views_count }} مشاهدة</span>
                        </div>
                        
                        <a href="{{ related_article.get_absolute_url }}" 
                           class="inline-block bg-primary text-white px-4 py-2 rounded-md hover:bg-opacity-90 transition-colors">
                            اقرأ المزيد
                        </a>
                    </div>
                </article>
            {% endfor %}
        </div>
    </div>
</section>
{% endif %}

<!-- Back to Home -->
<section class="py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <a href="{% url 'blog:home' %}" 
           class="inline-block bg-primary text-white px-8 py-3 rounded-lg hover:bg-opacity-90 transition-colors btn-hover">
            <i class="fas fa-arrow-right ml-2"></i>العودة إلى الرئيسية
        </a>
    </div>
</section>
{% endblock %}

{% block extra_css %}
<style>
    /* Enhanced prose styling for Arabic content */
    .prose {
        font-size: 1.125rem;
        line-height: 1.8;
    }
    
    .prose p {
        margin-bottom: 1.5rem;
        text-align: justify;
    }
    
    .prose h2, .prose h3, .prose h4 {
        color: #1f2937;
        font-weight: 700;
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    
    .prose h2 {
        font-size: 1.5rem;
        border-bottom: 2px solid #00BFA6;
        padding-bottom: 0.5rem;
    }
    
    .prose h3 {
        font-size: 1.25rem;
    }
    
    .prose ul, .prose ol {
        margin: 1.5rem 0;
        padding-right: 1.5rem;
    }
    
    .prose li {
        margin-bottom: 0.5rem;
    }
    
    .prose blockquote {
        border-right: 4px solid #00BFA6;
        padding-right: 1rem;
        margin: 2rem 0;
        font-style: italic;
        background-color: #f9fafb;
        padding: 1rem;
        border-radius: 0.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    function copyToClipboard() {
        navigator.clipboard.writeText(window.location.href).then(function() {
            // Show success message
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-check ml-2"></i>تم النسخ';
            button.classList.add('bg-green-600');
            button.classList.remove('bg-gray-600');
            
            setTimeout(function() {
                button.innerHTML = originalText;
                button.classList.remove('bg-green-600');
                button.classList.add('bg-gray-600');
            }, 2000);
        });
    }
    
    // Reading progress indicator
    window.addEventListener('scroll', function() {
        const article = document.querySelector('article');
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        
        // You can add a progress bar here if needed
    });
</script>
{% endblock %}
